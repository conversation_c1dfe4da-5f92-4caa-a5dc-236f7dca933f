<script setup lang="ts">
import type { Key } from 'ant-design-vue/es/_util/type';

import type { DriveFileInfo } from '@vben/types';

import { h, nextTick, reactive, ref, watch } from 'vue';

import { confirm } from '@vben/common-ui';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { DeleteOutlined } from '@ant-design/icons-vue';
import {
  Breadcrumb,
  BreadcrumbItem,
  Button,
  Form,
  FormItem,
  Input,
  message,
  Modal,
  Space,
  Table,
  TabPane,
  Tabs,
} from 'ant-design-vue';

import audioImg from '#/assets/images/document/audio.png';
import blankImg from '#/assets/images/document/blank.png';
import codeImg from '#/assets/images/document/code.png';
import excelImg from '#/assets/images/document/excel.png';
import folderImg from '#/assets/images/document/folder.png';
import imageImg from '#/assets/images/document/image.png';
import pdfImg from '#/assets/images/document/pdf.png';
import pptImg from '#/assets/images/document/ppt.png';
import rarImg from '#/assets/images/document/rar.png';
import txtImg from '#/assets/images/document/txt.png';
import wordImg from '#/assets/images/document/word.png';
import { BaseUpload, FilePreviewDialog, FileUploader } from '#/components';

const props = defineProps({
  apiSuite: { type: Object, default: () => ({}) },
  previewExternalApi: { type: Function, default: null },
  downloadApi: { type: Function, default: null },
  uploadApi: { type: Function, default: null },
});
const dropZoneRef = ref<HTMLDivElement | null>(null);
// 新增：拖拽计数器，解决闪烁问题
const dragCounter = ref(0);

const leftList = [
  { id: 'all', fileName: '我的文档', icon: 'akar-icons:file' },
  { id: 'shareToMe', fileName: '共享给我', icon: 'ri:user-received-line' },
];
const searchInfo = ref<{ keyword?: string; parentId: number }>({
  keyword: '',
  parentId: 0,
});
const levelList = ref<{ fileName?: string; id: number }[]>([]);
const selectedRowKeys = ref<number[]>([]);
const selectedRows = ref<DriveFileInfo[]>([]);
const fileList = ref<DriveFileInfo[]>([]);
const store = reactive<{ activeKey: 'all' | 'shareToMe'; loading: boolean; visible: boolean }>({
  visible: false,
  activeKey: 'all',
  loading: false,
});
const columns = [{ title: '文件名', dataIndex: 'fileName', key: 'fileName' }];
const fileUploaderRef = ref();
const handleReturnToPrevious = () => {
  if (levelList.value.length > 1) {
    const previousItem = levelList.value[levelList.value.length - 2];
    if (previousItem) {
      handleJump(previousItem as { id: number }, levelList.value.length - 2);
    }
  }
};
const initData = () => {
  store.loading = true;
  selectedRowKeys.value = [];
  selectedRows.value = [];
  const api = {
    all: props.apiSuite.getCloudDiskListApi,
    shareToMe: props.apiSuite.getCloudDiskShareToMeListApi,
  };
  api[store.activeKey](searchInfo.value).then((res: DriveFileInfo[]) => {
    fileList.value = res;
    store.loading = false;
  });
};
const handleReset = () => {
  searchInfo.value.keyword = '';
  initData();
};
const handleJump = (item: { id: number }, i: number) => {
  searchInfo.value.parentId = item.id;
  levelList.value = levelList.value.slice(0, i + 1);
  handleReset();
};
const resetBreadcrumb = () => {
  const activeItem = leftList.find((o) => o.id === store.activeKey);
  levelList.value = [{ id: 0, fileName: activeItem?.fileName }];
  searchInfo.value.parentId = 0;
};
const search = () => {
  if (searchInfo.value.keyword) resetBreadcrumb();
  initData();
};
const reset = () => {
  searchInfo.value.keyword = '';
  initData();
};
const handleDelete = async () => {
  await confirm('您确定要把所选文件放入回收站, 是否继续?', '确认删除');
  props.apiSuite.delCloudDiskFileApi({ ids: selectedRowKeys.value }).then(() => {
    message.success('删除成功');
    initData();
  });
};
const FilePreviewDialogRef = ref();
const handlePreview = async (record: DriveFileInfo) => {
  if (record.id) {
    FilePreviewDialogRef.value.init(record.fileId);
  }
};
const openFolder = (record: DriveFileInfo) => {
  if (record.id) {
    searchInfo.value.parentId = record.id;
    levelList.value.push({ id: record.id, fileName: record.fileName });
    selectedRowKeys.value = [];
    selectedRows.value = [];
    handleReset();
  }
};
const onRecordClick = (record: DriveFileInfo) => {
  record.fileType ? openFolder(record) : handlePreview(record);
};
const onSelectChange = (keys: Key[], Rows: DriveFileInfo[]) => {
  if (config.value.multiple) {
    selectedRowKeys.value = keys as number[];
    selectedRows.value = Rows;
  } else {
    selectedRowKeys.value = keys.length > 0 ? [keys[keys.length - 1] as number] : [];
    selectedRows.value = Rows.length > 0 ? [Rows[Rows.length - 1] as DriveFileInfo] : [];
  }
};
const init = () => {
  resetBreadcrumb();
  initData();
};
interface ConfigInfo {
  multiple: boolean;
}
const config = ref<ConfigInfo>({
  multiple: false,
});
let globalResolve: ((value: { file: DriveFileInfo | DriveFileInfo[]; id: number | number[] }) => void) | null = null;
let globalReject: ((reason?: unknown) => void) | null = null;
const pick = (options?: Partial<ConfigInfo>) => {
  return new Promise((resolve, reject) => {
    globalResolve = resolve;
    globalReject = reject;
    config.value = Object.assign(config.value, options);
    store.visible = true;
    init();
  });
};
const imgTypeList = new Set(['bmp', 'gif', 'jpeg', 'jpg', 'png']);
const wordTypeList = new Set(['doc', 'docx']);
const excelTypeList = new Set(['xls', 'xlsx']);
const pptTypeList = new Set(['ppt', 'pptx']);
const pdfTypeList = new Set(['pdf']);
const zipTypeList = new Set(['7z', 'arj', 'rar', 'z', 'zip']);
const txtTypeList = new Set(['log', 'txt']);
const codeTypeList = new Set(['cs', 'html', 'xml']);
const videoTypeList = new Set([
  'avi',
  'avi',
  'flv',
  'flv',
  'mkv',
  'mov',
  'mp3',
  'mp4',
  'mpeg',
  'mpg',
  'mpg',
  'ram',
  'rm',
  'rm',
  'rmvb',
  'swf',
  'wma',
  'wmv',
]);
const getRecordImg = (ext?: string) => {
  if (!ext) return folderImg;
  if (ext) ext = ext.replace('.', '');
  if (wordTypeList.has(ext)) return wordImg;
  if (excelTypeList.has(ext)) return excelImg;
  if (pptTypeList.has(ext)) return pptImg;
  if (pdfTypeList.has(ext)) return pdfImg;
  if (zipTypeList.has(ext)) return rarImg;
  if (txtTypeList.has(ext)) return txtImg;
  if (codeTypeList.has(ext)) return codeImg;
  if (imgTypeList.has(ext)) return imageImg;
  if (videoTypeList.has(ext)) return audioImg;
  return blankImg;
};
const confirmPick = () => {
  if (selectedRowKeys.value.length === 0) {
    return message.error('请选择文件');
  }
  const fileIds = selectedRows.value.map((o) => o.fileId as number);
  const id = config.value.multiple ? fileIds : (fileIds[0] as number);
  const file = config.value.multiple ? selectedRows.value : (selectedRows.value[0] as DriveFileInfo);
  globalResolve && globalResolve({ id, file });
  globalResolve = null;
  globalReject = null;
  store.visible = false;
};
const directUploadSuccess = (res: { attachId: number; originalName: string }) => {
  const id = config.value.multiple ? [res.attachId] : res.attachId;
  const resData: { attachId: number; fileId?: number; fileName?: string; mainName?: string; originalName: string } =
    res;
  resData.fileId = resData.attachId;
  resData.fileName = resData.originalName;
  const parts = resData.originalName.split('.');
  if (parts.length > 1) {
    parts.pop();
    resData.mainName = parts.join('.');
  } else {
    resData.mainName = resData.originalName;
  }
  const file = config.value.multiple ? [res] : res;
  globalResolve && globalResolve({ id, file });
  globalResolve = null;
  globalReject = null;
  store.visible = false;
};
const afterClose = () => {
  globalReject && globalReject();
  globalResolve = null;
  globalReject = null;
};
defineExpose({ pick });

// --- 拖拽上传逻辑优化 ---
const handleDragEnter = (event: DragEvent) => {
  event.preventDefault();
  dragCounter.value++;
  if (dragCounter.value === 1) {
    dropZoneRef.value?.classList.add('drag-over-active');
  }
};

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault();
  dragCounter.value--;
  if (dragCounter.value === 0) {
    dropZoneRef.value?.classList.remove('drag-over-active');
  }
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  dragCounter.value = 0; // 重置计数器
  dropZoneRef.value?.classList.remove('drag-over-active');

  const items = event.dataTransfer?.items;
  if (items) {
    for (const item of items) {
      const entry = item.webkitGetAsEntry();
      if (entry && entry.isDirectory) {
        message.warning('不支持拖拽文件夹上传，请直接拖拽文件。');
        return;
      }
    }
  }

  const files = event.dataTransfer?.files;
  if (store.activeKey === 'all' && fileUploaderRef.value && files && files.length > 0) {
    fileUploaderRef.value.uploadFiles([...files]);
  }
};

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
};

watch(
  () => store.visible,
  (isVisible) => {
    nextTick(() => {
      if (isVisible && dropZoneRef.value) {
        dropZoneRef.value.addEventListener('dragenter', handleDragEnter);
        dropZoneRef.value.addEventListener('dragleave', handleDragLeave);
        dropZoneRef.value.addEventListener('drop', handleDrop);
        dropZoneRef.value.addEventListener('dragover', handleDragOver);
      } else if (dropZoneRef.value) {
        dragCounter.value = 0; // 确保关闭时重置计数器
        dropZoneRef.value.classList.remove('drag-over-active'); // 确保关闭时移除样式
        dropZoneRef.value.removeEventListener('dragenter', handleDragEnter);
        dropZoneRef.value.removeEventListener('dragleave', handleDragLeave);
        dropZoneRef.value.removeEventListener('drop', handleDrop);
        dropZoneRef.value.removeEventListener('dragover', handleDragOver);
      }
    });
  },
);
</script>

<template>
  <Modal v-model:open="store.visible" title="选择文件" width="1200px" :after-close="afterClose">
    <div class="cloud-disk-file-picker" ref="dropZoneRef">
      <Tabs v-model:active-key="store.activeKey" tab-position="left" class="mr-3 mt-3 h-full border-r" @change="init">
        <TabPane v-for="tab in leftList" :key="tab.id">
          <template #tab>
            <div class="flex items-center">
              <VbenIcon :icon="tab.icon" class="mr-1" />
              <span>{{ tab.fileName }}</span>
            </div>
          </template>
        </TabPane>
      </Tabs>
      <div class="w-full">
        <Breadcrumb class="mb-3">
          <BreadcrumbItem v-if="levelList.length > 1" @click="handleReturnToPrevious">
            <a>返回上一级</a>
          </BreadcrumbItem>
          <BreadcrumbItem v-for="(item, i) in levelList" :key="i">
            <span v-if="i + 1 >= levelList.length">{{ item.fileName }}</span>
            <a v-else @click="handleJump(item, i)">{{ item.fileName }}</a>
          </BreadcrumbItem>
        </Breadcrumb>
        <div class="mb-3 flex items-center justify-between">
          <Form layout="inline" :model="searchInfo">
            <FormItem label="关键词">
              <Input v-model:value="searchInfo.keyword" />
            </FormItem>
            <FormItem>
              <Button type="primary" @click="search">查询</Button>
            </FormItem>
            <FormItem>
              <Button @click="reset">重置</Button>
            </FormItem>
          </Form>
          <div>
            <template v-if="selectedRowKeys.length === 0">
              <FileUploader
                ref="fileUploaderRef"
                v-if="store.activeKey === 'all'"
                :upload-api="apiSuite.uploadCloudDiskFileApi"
                :pre-check-api="apiSuite.preCheckCloudDiskFileApi"
                :check-api="apiSuite.checkCloudDiskFileApi"
                :folder-id="searchInfo.parentId"
                class="mr-2"
                @all-completed="initData"
              />
            </template>
            <template v-else>
              <template v-if="store.activeKey === 'all'">
                <Button :icon="h(DeleteOutlined)" @click="handleDelete">删除</Button>
              </template>
            </template>
          </div>
        </div>
        <Table
          :data-source="fileList"
          :columns="columns"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          row-key="id"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'fileName'">
              <span class="flex cursor-pointer items-center" @click="onRecordClick(record)">
                <img :src="getRecordImg(record.fileExtension)" class="mr-1 h-4 w-4" alt="" />
                <span>{{ record.fileName }}</span>
              </span>
            </template>
          </template>
        </Table>
      </div>
    </div>
    <FilePreviewDialog ref="FilePreviewDialogRef" :preview-api="previewExternalApi" :download-api="downloadApi" />
    <template #footer>
      <Space>
        <Button @click="store.visible = false">取消</Button>
        <BaseUpload
          :preview-api="downloadApi"
          :upload-api="uploadApi"
          list-type="text"
          btn-text="直接上传"
          @upload-success="directUploadSuccess"
        />
        <Button type="primary" @click="confirmPick">确认</Button>
      </Space>
    </template>
  </Modal>
</template>

<style lang="less">
.cloud-disk-file-picker {
  display: flex;
  .ant-tabs-content-holder {
    display: none;
  }
}

// 拖拽高亮样式
.cloud-disk-file-picker {
  // 为子元素的定位做准备
  position: relative;
}

// 定义一个名为 'drag-over-active' 的高亮类
.cloud-disk-file-picker.drag-over-active {
  // 使用 ::after 伪元素创建一个覆盖层
  &::after {
    content: '拖放到此处以上传'; // 覆盖层上显示的文字
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    // 视觉样式
    background-color: rgba(24, 144, 255, 0.1); // 淡蓝色半透明背景
    border: 2px dashed #1890ff; // 蓝色虚线边框
    border-radius: 8px;
    box-sizing: border-box;
    z-index: 10; // 确保在最上层显示

    // 文字和图标样式
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 500;
    color: #1890ff; // 蓝色文字
    pointer-events: none; // 关键：让覆盖层不影响鼠标事件
  }
}
</style>
