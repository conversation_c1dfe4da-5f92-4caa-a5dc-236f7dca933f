<script setup lang="ts">
import { ref, watch } from 'vue';

import { DeleteOutlined } from '@ant-design/icons-vue';
import { TypographyLink } from 'ant-design-vue';

import { FilePreviewDialog } from '#/components';

interface FileInfo {
  id: number;
  originalName: string;
  link: string;
}

const props = defineProps({
  editMode: { type: Boolean, default: false },
  listType: { type: String, default: 'text' },
  fileInfoApi: { type: Function, required: true },
  previewExternalApi: { type: Function, default: null },
});
const FilePreviewDialogRef = ref();
const fileIds = defineModel('fileIds', { type: Array });
const fileList = ref<FileInfo[]>([]);
const fileId = defineModel({ type: Number });
watch(
  () => fileId.value,
  async (fileId) => {
    if (fileId) {
      fileList.value = await props.fileInfoApi([fileId]);
    }
  },
  { immediate: true },
);
const preview = (item: FileInfo) => {
  FilePreviewDialogRef.value.init(item.id);
};
const deleteFile = (index: number) => {
  fileList.value.splice(index, 1);
  if (fileIds.value && fileIds.value.length > 0) {
    fileIds.value = fileList.value.map((item) => item.id);
  }
  if (fileId.value) {
    fileId.value = undefined;
  }
};
</script>

<template>
  <div>
    <template v-if="listType === 'text'">
      <TypographyLink v-for="(item, index) in fileList" :key="item.id" class="item-center flex">
        <span @click="preview(item)">{{ item.originalName }}</span>
        <DeleteOutlined v-if="editMode" class="ml-1" @click="deleteFile(index)" />
      </TypographyLink>
    </template>
    <div v-else-if="listType === 'picture-card'" class="flex flex-wrap">
      <div v-for="item in fileList" :key="item.id" class="h-24 w-24">
        <img
          :src="item.link"
          :alt="item.originalName"
          class="h-full w-full cursor-pointer object-cover"
          @click="preview(item)"
        />
      </div>
    </div>
    <FilePreviewDialog ref="FilePreviewDialogRef" :preview-api="previewExternalApi" />
  </div>
</template>

<style scoped></style>
