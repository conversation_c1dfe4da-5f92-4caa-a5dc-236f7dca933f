<script setup lang="ts">
import type { TableColumnsType } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';

import type { VbenFormProps } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { DictInfo, DictTagInfo, DictTypeInfo } from '#/api';

import { computed, h, nextTick, reactive, ref } from 'vue';

import { ImportData, StatusTag } from '@vben/base-ui';
import { ColorPicker, IconPicker, Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep, defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { ClearOutlined, ExportOutlined, ImportOutlined, PlusOutlined, TagOutlined } from '@ant-design/icons-vue';
import {
  Modal as AntdModal,
  Button,
  Descriptions,
  DescriptionsItem,
  Form,
  FormItem,
  Input,
  InputNumber,
  message,
  Radio,
  Select,
  Space,
  Switch,
  Table,
  Tag,
  Textarea,
  TypographyLink,
} from 'ant-design-vue';
import { defaultsDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addDictItemApi,
  addDictTagApi,
  addDictTypeApi,
  clearDictCacheApi,
  delDictItemApi,
  delDictTagApi,
  delDictTypeApi,
  editDictItemApi,
  editDictTypeApi,
  exportDictItemApi,
  getDictItemListApi,
  getDictTagListApi,
  getDictTypePageListApi,
  importDictItemApi,
} from '#/api';

const dictStore = useDictStore();
const dictTagList = ref<DictTagInfo[]>([]);
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'code',
      label: '字典编码',
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: '字典名称',
    },
    {
      component: 'Select',
      fieldName: 'type',
      label: '字典类型',
      componentProps: {
        allowClear: true,
        showSearch: true,
        options: dictStore.getDictList('dictType'),
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'tag',
      label: '字典标签',
      componentProps: {
        allowClear: true,
        showSearch: true,
        api: getDictTagListApi,
        fieldNames: {
          label: 'name',
          value: 'name',
        },
      },
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'seq', type: 'seq', width: 80 },
    { field: 'code', title: '字典编码' },
    { field: 'name', title: '字典名称' },
    {
      field: 'type',
      title: '字典类型',
      formatter: ['formatStatus', 'dictType'],
    },
    { field: 'tag', title: '字典标签', slots: { default: 'tag' } },
    { field: 'sortCode', title: '排序' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 180,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const res = await getDictTypePageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
        res.records.forEach((item: DictTypeInfo & { tagList: string[] }) => {
          item.tagList = item.tag?.split(',');
        });
        return res;
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const loading = ref({
  dict: false,
  save: false,
});
const dictTypeData = ref<Partial<DictTypeInfo>>({});
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const dictColumns = computed(() => {
  const base: TableColumnsType = [
    {
      title: '序号',
      dataIndex: 'seq',
      key: 'seq',
      width: 80,
      customRender: ({ index }: { index: number }) => index + 1,
    },
    {
      title: '字典项名称',
      dataIndex: 'dictName',
      key: 'dictName',
      maxWidth: 300,
      ellipsis: true,
    },
    {
      title: '字典项键值',
      dataIndex: 'dictValue',
      key: 'dictValue',
      maxWidth: 250,
      ellipsis: true,
    },
    {
      title: '排序',
      dataIndex: 'sortCode',
      key: 'sortCode',
      width: 100,
    },
    {
      title: '权限',
      dataIndex: 'permission',
      key: 'permission',
      customRender: ({ text }: { text: string }) => {
        return h(StatusTag, { code: 'dictEditType', value: text });
      },
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      customRender: ({ text }: { text: number }) => {
        return h(StatusTag, { code: 'baseEnableType', value: text });
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
    },
  ];
  return dictTypeData.value.type === 'BIZ' ? base : base.filter((item) => item.key !== 'permission');
});
const [Modal, modalApi] = useVbenModal({
  onOpened() {
    getDictTagList();
  },
});
const dictItemModalTitle = ref('');
const dictItemList = ref<DictInfo[]>([]);
const [DictItemModal, dictItemModalApi] = useVbenModal({
  onConfirm: async () => {
    await dictItemFormRef.value.validate();
    let api = addDictItemApi;
    if (dictItemForm.value.id) {
      api = editDictItemApi;
    }
    try {
      loading.value.save = true;
      await api(dictItemForm.value as DictInfo);
      message.success($t('base.resSuccess'));
      await dictItemModalApi.close();
      dictItemList.value = await getDictItemListApi({
        code: dictTypeData.value.code as string,
      });
    } finally {
      loading.value.save = false;
    }
  },
  onClosed: () => {
    dictItemForm.value = cloneDeep(defaultDictItem);
    dictItemFormRef.value.resetFields();
  },
});
const set = async (row: DictTypeInfo) => {
  dictTypeData.value = cloneDeep(row);
  modalApi.open();
  dictItemList.value = await getDictItemListApi({ code: row.code });
};
const addDictItem = async () => {
  dictItemModalTitle.value = '新增字典项';
  dictItemModalApi.open();
  dictItemForm.value.code = dictTypeData.value.code;
  dictItemList.value = await getDictItemListApi({
    code: dictTypeData.value.code as string,
  });
};
const editDictItem = async (row: DictInfo) => {
  dictItemModalTitle.value = '编辑字典项';
  dictItemModalApi.open();
  dictItemForm.value = defaultsDeep(row, defaultDictItem);
};
const delDictItem = async (row: DictInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await delDictItemApi(row.id);
      message.success($t('base.resSuccess'));
      dictItemList.value = await getDictItemListApi({
        code: dictTypeData.value.code as string,
      });
    },
  });
};
const [DictTypeModal, dictTypeModalApi] = useVbenModal({
  onConfirm: async () => {
    await dictTypeFormRef.value.validate();
    let api = addDictTypeApi;
    if (dictTypeForm.value.id) {
      api = editDictTypeApi;
    }
    try {
      loading.value.save = true;
      await api(dictTypeForm.value as DictTypeInfo);
      message.success($t('base.resSuccess'));
      await dictTypeModalApi.close();
      await gridApi.reload();
    } finally {
      loading.value.save = false;
    }
  },
  onClosed: () => {
    dictTypeForm.value = { sortCode: 0, isTag: 0 };
    dictTypeFormRef.value.resetFields();
  },
});
const dictTypeFormRef = ref();
const dictTypeForm = ref<Partial<DictTypeInfo>>({
  sortCode: 0,
  isTag: 0,
  iconType: 0,
});
const dictTypeTagList = computed({
  get() {
    return dictTypeForm.value.tag?.split(',') || [];
  },
  set(value) {
    if (value) {
      dictTypeForm.value.tag = value.join(',');
    }
  },
});
const dictTypeRules: Record<string, Rule[]> = {
  code: [{ required: true, message: '请输入字典编码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入字典名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择字典类型', trigger: 'change' }],
};
const addDictType = async () => {
  dictTypeModalApi.open();
};
const editType = async (row: DictTypeInfo) => {
  dictTypeForm.value = cloneDeep(row);
  dictTypeModalApi.open();
};
const delType = async (row: DictTypeInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await delDictTypeApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const dictItemFormRef = ref();
const defaultDictItem = {
  sortCode: 0,
  enabled: 1,
  dictColor: 'default',
  iconType: 0,
};
const dictItemForm = ref<Partial<DictInfo>>(cloneDeep(defaultDictItem));
const dictItemRules: Record<string, Rule[]> = {
  dictName: [{ required: true, message: '请输入字典项名称', trigger: 'blur' }],
  dictValue: [{ required: true, message: '请输入字典项键值', trigger: 'blur' }],
  dictIcon: [{ required: true, message: '请输入字典项图标', trigger: 'change' }],
  permission: [{ required: true, message: '请选择权限', trigger: 'change' }],
  dictColor: [{ required: true, message: '请选择字典颜色', trigger: 'change' }],
};
const clearCache = async () => {
  AntdModal.confirm({
    title: '清除缓存',
    content: '确认清除缓存？',
    async onOk() {
      await clearDictCacheApi();
      message.success($t('base.resSuccess'));
    },
  });
};
const iconTypeList = [
  { label: '无图标', value: 0 },
  { label: 'ICON图标', value: 1 },
  { label: 'SVG图标', value: 2 },
];
const svgVisible = ref(false);
const activeDictItem = ref<DictInfo>({} as DictInfo);
const editSvg = (row: DictInfo) => {
  svgVisible.value = true;
  activeDictItem.value = row;
};
const confirmSetSvg = () => {
  svgVisible.value = false;
  dictItemForm.value.dictIcon = activeDictItem.value.dictIcon;
};
const [DictTagModal, dictTagModalApi] = useVbenModal({
  showConfirmButton: false,
  cancelText: '完成',
});
const state = reactive({
  inputVisible: false,
  inputValue: '',
});
const dictTagInputRef = ref();
const getDictTagList = async () => {
  dictTagList.value = await getDictTagListApi();
};
getDictTagList();
const handleDictTagRemove = (removedTag: DictTagInfo) => {
  AntdModal.confirm({
    title: '确认删除',
    content: '确认删除此标签？',
    async onOk() {
      await delDictTagApi(removedTag.id as number);
      await getDictTagList();
    },
  });
};
const showDictTagInput = () => {
  state.inputVisible = true;
  nextTick(() => {
    dictTagInputRef.value.focus();
  });
};
const handleDictTagInputConfirm = async () => {
  const inputValue = state.inputValue;
  const tags = dictTagList.value;
  if (!inputValue) {
    return;
  }
  if (tags.some((item: DictTagInfo) => item.name === inputValue)) {
    return message.error('标签已存在');
  }
  await addDictTagApi({ name: inputValue });
  await getDictTagList();
  state.inputValue = '';
  state.inputVisible = false;
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <Button type="primary" :icon="h(PlusOutlined)" @click="addDictType">
            {{ $t('base.add') }}
          </Button>
          <Button type="primary" :icon="h(ExportOutlined)" @click="exportDictItemApi"> 导出 </Button>
          <ImportData
            :icon="h(ImportOutlined)"
            :upload-api="importDictItemApi"
            :download-template-api="exportDictItemApi"
            @import-success="gridApi.reload"
          />
          <Button type="primary" :icon="h(TagOutlined)" @click="dictTagModalApi.open"> 字典标签 </Button>
          <Button danger :icon="h(ClearOutlined)" @click="clearCache"> 清除缓存 </Button>
        </a-space>
      </template>
      <template #tag="{ row }">
        <a-tag v-for="tag in row.tagList" :key="tag" color="blue">
          {{ tag }}
        </a-tag>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="set(row)">字典配置</TypographyLink>
          <TypographyLink @click="editType(row)">{{ $t('base.edit') }}</TypographyLink>
          <TypographyLink type="danger" @click="delType(row)">{{ $t('base.del') }}</TypographyLink>
        </Space>
      </template>
    </Grid>
    <Modal
      :confirm-loading="loading.dict"
      :show-confirm-button="false"
      cancel-text="关闭"
      class="w-[1000px]"
      title="字典配置"
    >
      <Descriptions>
        <DescriptionsItem label="字典编码">
          {{ dictTypeData.code }}
        </DescriptionsItem>
        <DescriptionsItem label="字典名称">
          {{ dictTypeData.name }}
        </DescriptionsItem>
      </Descriptions>
      <div class="mb-4">
        <Button class="mr-2" type="primary" @click="addDictItem">
          {{ $t('base.add') }}
        </Button>
      </div>
      <Table :data-source="dictItemList" :columns="dictColumns">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'dictName'">
            <Tag v-if="dictTypeData.isTag" :color="record.dictColor">
              <div class="flex items-center">
                <VbenIcon v-if="dictTypeData.iconType" :icon="record.dictIcon" class="mr-1 w-4" />
                <span>{{ record.dictName }}</span>
              </div>
            </Tag>
            <div v-else class="flex items-center">
              <VbenIcon v-if="dictTypeData.iconType" :icon="record.dictIcon" class="mr-1 w-4" />
              <span>{{ record.dictName }}</span>
            </div>
          </template>
          <template v-if="column.key === 'action'">
            <Space>
              <TypographyLink @click="editDictItem(record as DictInfo)">
                {{ $t('base.edit') }}
              </TypographyLink>
              <TypographyLink type="danger" @click="delDictItem(record as DictInfo)">
                {{ $t('base.del') }}
              </TypographyLink>
            </Space>
          </template>
        </template>
      </Table>
    </Modal>
    <DictItemModal :title="dictItemModalTitle" :submitting="loading.save">
      <!--<DictItemForm />-->
      <Form
        ref="dictItemFormRef"
        :colon="false"
        :model="dictItemForm"
        :rules="dictItemRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <FormItem name="dictName" label="字典项名称">
          <Input v-model:value="dictItemForm.dictName" />
        </FormItem>
        <FormItem name="dictValue" label="字典项键值">
          <Input v-model:value="dictItemForm.dictValue" />
        </FormItem>
        <FormItem v-if="dictTypeData.iconType" name="dictIcon" label="字典项图标">
          <IconPicker v-if="dictTypeData.iconType === 1" v-model="dictItemForm.dictIcon" />
          <div v-else class="flex items-center">
            <VbenIcon
              v-if="dictItemForm.dictIcon"
              :icon="dictItemForm.dictIcon"
              class="h-8 w-8 cursor-pointer rounded-lg border"
              @click="editSvg(dictItemForm as DictInfo)"
            />
            <Button v-else :icon="h(PlusOutlined)" class="mr-2" @click="editSvg(dictItemForm as DictInfo)" />
          </div>
        </FormItem>
        <FormItem v-if="dictTypeData.type === 'BIZ'" name="permission" label="权限">
          <Select v-model:value="dictItemForm.permission" :options="dictStore.getDictList('dictEditType')" />
        </FormItem>
        <FormItem v-if="dictTypeData.isTag === 1" name="dictColor" label="字典项颜色">
          <Input v-model:value="dictItemForm.dictColor">
            <template #addonBefore>
              <ColorPicker v-model="dictItemForm.dictColor as string" />
            </template>
          </Input>
        </FormItem>
        <FormItem name="sortCode" label="排序">
          <InputNumber v-model:value="dictItemForm.sortCode" :min="0" :step="1" :precision="0" class="w-full" />
        </FormItem>
        <FormItem name="enabled" label="状态">
          <Switch v-model:checked="dictItemForm.enabled" :checked-value="1" :un-checked-value="0" />
        </FormItem>
      </Form>
    </DictItemModal>
    <DictTypeModal title="新增字典类型" :submitting="loading.save">
      <Form
        ref="dictTypeFormRef"
        :colon="false"
        :model="dictTypeForm"
        :rules="dictTypeRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <FormItem name="name" label="字典名称">
          <Input v-model:value="dictTypeForm.name" />
        </FormItem>
        <FormItem name="code" label="字典编码">
          <Input v-model:value="dictTypeForm.code" />
        </FormItem>
        <FormItem name="type" label="字典类型">
          <Select v-model:value="dictTypeForm.type" :options="dictStore.getDictList('dictType')" />
        </FormItem>
        <FormItem name="sortCode" label="排序">
          <InputNumber v-model:value="dictTypeForm.sortCode" :min="0" :step="1" :precision="0" class="w-full" />
        </FormItem>
        <FormItem name="isTag" label="是否标签">
          <Switch v-model:checked="dictTypeForm.isTag" :checked-value="1" :un-checked-value="0" />
        </FormItem>
        <FormItem name="iconType" label="图标类型">
          <Radio.Group
            v-model:value="dictTypeForm.iconType"
            :options="iconTypeList"
            button-style="solid"
            option-type="button"
          />
        </FormItem>
        <FormItem name="tag" label="标签">
          <Select
            v-model:value="dictTypeTagList"
            :options="dictTagList"
            :field-names="{ label: 'name', value: 'name' }"
            mode="tags"
          />
        </FormItem>
      </Form>
    </DictTypeModal>
    <DictTagModal title="字典标签">
      <a-space :wrap="true">
        <a-tag
          v-for="item in dictTagList"
          :key="item.id"
          closable
          color="blue"
          @close="handleDictTagRemove(item)"
          class="dict-tag-item"
        >
          {{ item.name }}
        </a-tag>
        <a-input
          v-if="state.inputVisible"
          ref="dictTagInputRef"
          v-model:value="state.inputValue"
          type="text"
          size="small"
          :style="{ width: '78px' }"
          @blur="handleDictTagInputConfirm"
          @keyup.enter="handleDictTagInputConfirm"
        />
        <a-tag v-else class="dict-tag-item cursor-pointer" @click="showDictTagInput">
          <PlusOutlined />
          添加
        </a-tag>
      </a-space>
    </DictTagModal>
    <AntdModal v-model:open="svgVisible" title="请输入SVG代码" @ok="confirmSetSvg">
      <Textarea v-model:value="activeDictItem.dictIcon" :autosize="{ minRows: 5, maxRows: 8 }" />
    </AntdModal>
  </Page>
</template>

<style>
.dict-tag-item,
.dict-tag-item .ant-tag-close-icon {
  font-size: 14px;
}
</style>
