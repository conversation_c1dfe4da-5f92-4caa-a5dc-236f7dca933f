<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { ReceivableContractVO, ReceivableInvoiceVO, ReceivableVO } from '#/api';

import { computed, reactive, ref, watch } from 'vue';

import { ImportData } from '@vben/base-ui';
import { ApiComponent } from '@vben/common-ui';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';
import { cloneDeep } from '@vben/utils';

import {
  Button,
  Col,
  DatePicker,
  Form,
  FormItem,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Textarea,
  TypographyLink,
  TypographyText,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import { BaseAttachmentList, BaseFilePickList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getCompanyListApi, invoiceCheckApi, invoiceDownloadTemplateApi, invoiceImportApi, invoiceOcrApi } from '#/api';

const emit = defineEmits(['register', 'ok']);

const dictStore = useDictStore();
const baseFormInfo = reactive<ReceivableVO>({
  attachmentList: [],
  projectId: [],
  projectName: '',
  absBasisAssetId: undefined,
  bizType: undefined,
  contractList: [],
  creditorDebtorDel: '',
  creditorList: [],
  creditorName: '',
  debtorList: [],
  debtorName: '',
  id: undefined,
  invoiceList: [],
  receivableAmount: 0,
  receivableCode: '',
  receivableDueDate: undefined,
  receivableName: '',
  receivableTerm: 0,
  remarks: '',
  status: '',
  zdCode: '',
  zdStatus: '',
  isSubmit: false,
});

const colSpan = { md: 12, sm: 24 };
// 根据接口必填字段定义验证规则
const rules: Record<string, Rule[]> = {
  creditorList: [{ required: true, message: '请输入债权人' }],
  debtorList: [{ required: true, message: '请输入债务人' }],
  receivableName: [{ required: true, message: '请输入应收账款名称' }],
  receivableAmount: [{ required: true, message: '请输入应收账款金额' }],
  receivableDueDate: [{ required: true, message: '请选择应收账款到期日期', trigger: 'change' }],
  receivableTerm: [{ required: true, message: '请选择应收账款期限' }],
};
const title = computed(() => {
  return baseFormInfo.id ? '编辑' : '新增';
});

const formRef = ref();

// 添加初始化方法
const init = (data: ReceivableVO) => {
  invoiceFile.value = '';
  if (data && Object.keys(data).length > 0) {
    // 如果有传入数据，则使用传入数据初始化表单

    Object.assign(baseFormInfo, cloneDeep(data));
    baseFormInfo.receivableDueDate = dayjs(data.receivableDueDate).valueOf().toString();
    // 提取映射逻辑为函数
    const mapCompanyList = (list) =>
      list.map((item) => ({
        label: item.companyName,
        key: item.companyCode,
      }));

    creditorList.value = mapCompanyList(data.creditorList);
    debtorList.value = mapCompanyList(data.debtorList);

    // 初始化表格数据
    data.contractList?.forEach((v) => (v.signedDate = dayjs(v.signedDate).valueOf().toString()));
    data.invoiceList?.forEach((v) => (v.invoiceDate = dayjs(v.invoiceDate).valueOf().toString()));
    baseFormInfo.contractList = data.contractList || [];
    baseFormInfo.invoiceList = data.invoiceList || [];

    gridApiContract.grid?.reloadData(baseFormInfo.contractList);
    gridApiInvoice.grid?.reloadData(baseFormInfo.invoiceList);
  } else {
    // 否则重置为初始状态
    Object.assign(baseFormInfo, {
      projectId: [],
      projectName: '',
      absBasisAssetId: undefined,
      bizType: undefined,
      contractList: [],
      creditorDebtorDel: '',
      creditorList: [],
      creditorName: '',
      debtorList: [],
      debtorName: '',
      id: undefined,
      invoiceList: [],
      receivableAmount: 0,
      receivableCode: '',
      receivableDueDate: undefined,
      receivableName: '',
      receivableTerm: 0,
      remarks: '',
      status: '',
      zdCode: '',
      zdStatus: '',
      isSubmit: false,
      attachmentList: [],
    });
    creditorList.value = [];
    debtorList.value = [];
    // 重置表格
    gridApiContract.grid?.reloadData([]);
    gridApiInvoice.grid?.reloadData([]);
  }
};

const [registerPopup, { changeOkLoading }] = usePopupInner((data) => init(data));
const labelCol = { style: { width: '150px' } };

const gridContractTable: VxeGridProps<ReceivableContractVO> = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    {
      field: 'contractName',
      title: '基础合同名称',
      slots: { default: 'contractName-input' },
      width: 180,
    },
    {
      field: 'contractCode',
      title: '基础合同编号',
      slots: { default: 'contractCode-input' },
      width: 180,
    },
    {
      field: 'contractType',
      title: '基础合同类型',
      slots: { default: 'contractType-select' },
      width: 180,
    },
    {
      field: 'totalAmount',
      title: '基础合同金额（元）',
      slots: { default: 'totalAmount-input' },
      width: 180,
    },
    {
      field: 'unpaidAmount',
      title: '未付款金额（元）',
      slots: { default: 'unpaidAmount-input' },
      width: 180,
    },
    {
      field: 'transferAmount',
      title: '拟转让应收账款金额（元）',
      slots: { default: 'transferAmount-input' },
      width: 180,
    },
    {
      field: 'voucherName',
      title: '凭证名称',
      slots: { default: 'voucherName-input' },
      width: 180,
    },
    {
      field: 'voucherNumber',
      title: '凭证号码',
      slots: { default: 'voucherNumber-input' },
      width: 180,
    },
    {
      field: 'signedDate',
      title: '签署日期',
      slots: { default: 'signedDate-date' },
      width: 180,
    },
    // {
    //   field: 'file',
    //   title: '上传附件',
    //   fixed: 'right',
    //   width: 160,
    //   slots: { default: 'upLoadFile' },
    // },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  data: baseFormInfo.contractList || [],
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const loading = ref(false);
const gridInvoiceTable: VxeGridProps<ReceivableInvoiceVO> = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  data: baseFormInfo.invoiceList || [],
  columns: [
    { field: 'invoiceType', title: '发票类型', slots: { default: 'invoiceType-input' }, width: 180 },
    { field: 'invoiceNumber', title: '发票号码', slots: { default: 'invoiceNumber-input' }, width: 180 },
    { field: 'invoiceCode', title: '发票代码', slots: { default: 'invoiceCode-input' }, width: 180 },
    { field: 'totalAmount', title: '不含税金额（元）', slots: { default: 'totalAmount-input' }, width: 180 },
    { field: 'totalAmountTax', title: '含税金额（元）', slots: { default: 'totalAmountTax-input' }, width: 180 },
    { field: 'invoiceDate', title: '开票日期', slots: { default: 'invoiceDate-input' }, width: 180 },
    { field: 'buyerName', title: '购买方', slots: { default: 'buyerName-input' }, width: 180 },
    { field: 'sellerName', title: '销售方', slots: { default: 'sellerName-input' }, width: 180 },
    { field: 'checkCode', title: '校验码后6位', slots: { default: 'checkCode-input' }, width: 180 },
    {
      field: 'verifyResult',
      title: '验真结果',
      formatter: ['formatStatus', 'RECEIVABLE_INVOICE_VERIFY_RESULT'],
      width: 100,
    },
    // {
    //   field: 'file',
    //   title: '发票附件',
    //   fixed: 'right',
    //   width: 160,
    //   slots: { default: 'upLoadFile' },
    // },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const [ContractTable, gridApiContract] = useVbenVxeGrid({ gridOptions: gridContractTable });
const [InvoiceTable, gridApiInvoice] = useVbenVxeGrid({ gridOptions: gridInvoiceTable });

const addContract = (gridApi: any) => {
  const newRow: ReceivableContractVO = {
    contractName: '',
    contractCode: '',
    contractType: undefined,
    totalAmount: 0,
    unpaidAmount: 0,
    transferAmount: 0,
    voucherName: '',
    voucherNumber: '',
    signedDate: undefined,
  };
  const $grid = gridApi.grid;
  if ($grid) $grid.insert(newRow);
};
const addInvoice = (gridApi: any) => {
  const newRow: ReceivableInvoiceVO = {
    invoiceType: undefined,
    invoiceNumber: '',
    invoiceCode: '',
    totalAmount: 0,
    totalAmountTax: 0,
    invoiceDate: '',
    buyerName: '',
    sellerName: '',
    checkCode: '',
    verifyResult: '0',
  };
  const $grid = gridApi.grid;
  if ($grid) $grid.insert(newRow);
};

const handleSave = async (isSubmit = false) => {
  try {
    await formRef.value.validate();
    // 确保获取表格最新数据
    if (gridApiContract.grid) {
      const ContractTableData = gridApiContract.grid.getTableData();
      baseFormInfo.contractList = ContractTableData.fullData;
    }
    // 确保获取表格最新数据
    if (gridApiInvoice.grid) {
      const InvoiceTableData = gridApiInvoice.grid.getTableData();
      baseFormInfo.invoiceList = InvoiceTableData.fullData;
    }
    const params = cloneDeep(baseFormInfo);
    if (isSubmit) {
      params.isSubmit = true;
    }
    emit('ok', params);
  } catch (error) {
    console.error(`${isSubmit ? '提交' : '保存'}失败:`, error);
  } finally {
    changeOkLoading(false);
  }
};

const save = async () => {
  handleSave(false);
};

const submit = async () => {
  handleSave(true);
};
// 修改删除合同行方法
const removeContract = (index: number) => {
  if (!baseFormInfo.contractList) return;
  const $grid = gridApiContract.grid;
  if ($grid) {
    const ContractTableData = $grid.getTableData();
    $grid.remove(ContractTableData.fullData[index]);
  }
};

const creditorHandleChange = (value: any) => {
  baseFormInfo.creditorList = value.map((item: any) => ({
    companyName: item.label,
    companyCode: item.key,
  }));
};

const debtorHandleChange = (value: any) => {
  baseFormInfo.debtorList = value.map((item: any) => ({
    companyName: item.label,
    companyCode: item.key,
  }));
};

const creditorList = ref([]);
const debtorList = ref([]);

// 删除发票行
const removeInvoice = (index: number) => {
  if (!baseFormInfo.invoiceList) return;
  const $grid = gridApiInvoice.grid;
  if ($grid) {
    const InvoiceTableData = $grid.getTableData();
    $grid.remove(InvoiceTableData.fullData[index]);
  }
};

const checkInvoice = async (row: any) => {
  try {
    const params = {
      invoiceCode: row.invoiceCode,
      billingDate: row.invoiceDate,
      totalAmount: row.totalAmount,
      amountTax: row.totalAmountTax,
      checkCode: row.checkCode,
      invoiceType: row.invoiceType,
      invoiceNumber: row.invoiceNumber,
    };
    const res: any = await invoiceCheckApi(params);
    const InvoiceTableData = gridApiInvoice.grid.getTableData();
    InvoiceTableData.fullData.forEach((item) => {
      if (item.invoiceNumber === res.invoiceNumber) {
        item.verifyResult = res ? '1' : '2';
      }
    });
    gridApiInvoice.grid.reloadData(InvoiceTableData.fullData);
  } catch (error) {
    message.error('发票验证失败:', error);
  }
};

const invoiceVerification = async (id: any) => {
  try {
    const res: any = await invoiceOcrApi([id]);
    if (res.successCount > 0 && res.successResults?.length > 0) {
      const newRow: ReceivableInvoiceVO = {
        invoiceType: res.successResults[0].invoiceType,
        invoiceNumber: res.successResults[0].invoiceNumber,
        invoiceCode: res.successResults[0].invoiceCode,
        totalAmount: res.successResults[0].totalAmount,
        totalAmountTax: Number(res.successResults[0].totalTax) + Number(res.successResults[0].totalAmount),
        invoiceDate: dayjs(res.successResults[0].billingDate, 'YYYYMMDD').valueOf(),
        buyerName: res.successResults[0].purchaserName,
        sellerName: res.successResults[0].salesName,
        checkCode: res.successResults[0].checkCode,
        verifyResult: '0',
      };
      const $grid = gridApiInvoice.grid;
      if ($grid) $grid.insert(newRow);
    }
  } catch {}
};

const handleImportSuccess = (data: any) => {
  try {
    data.forEach((item: any) => {
      item.invoiceDate = dayjs(item.invoiceDate).valueOf();
    });
    const $grid = gridApiInvoice.grid;
    if ($grid) $grid.insert(data);
  } catch {}
};

const invoiceFile = ref('');

watch(invoiceFile, (newValue) => {
  invoiceVerification(newValue);
});
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <template #centerToolbar>
      <Button class="ml-2" type="primary" @click="submit">提交</Button>
    </template>
    <Form
      ref="formRef"
      :colon="false"
      :model="baseFormInfo"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="应收款账编号" name="receivableCode">
            <Input disabled v-model:value="baseFormInfo.receivableCode" placeholder="应收款账编号自动生成" />
          </FormItem>
        </Col>
        <!-- <Col v-bind="colSpan">
          <FormItem label="业务类型" name="bizType">
            <Select v-model:value="baseFormInfo.bizType" :options="dictStore.getDictList('FCT_FACTORING_TYPE')" />
          </FormItem>
        </Col> -->
        <Col v-bind="colSpan">
          <FormItem label="债权人" name="creditorList">
            <ApiComponent
              v-model="creditorList"
              :component="Select"
              mode="multiple"
              label-in-value="true"
              :api="getCompanyListApi"
              @change="creditorHandleChange"
              label-field="companyName"
              value-field="companyCode"
              model-prop-name="value"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="债务人" name="debtorList">
            <ApiComponent
              v-model="debtorList"
              :component="Select"
              mode="multiple"
              :api="getCompanyListApi"
              label-field="companyName"
              label-in-value="true"
              value-field="companyCode"
              model-prop-name="value"
              @change="debtorHandleChange"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="应收款账名称" name="receivableName">
            <Input v-model:value="baseFormInfo.receivableName" placeholder="请输入应收款账名称" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="债权人、债务人关系" name="creditorDebtorDel">
            <Input v-model:value="baseFormInfo.creditorDebtorDel" placeholder="请输入债权人、债务人关系" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="应收账款金额（元）" name="receivableAmount">
            <Input v-model:value="baseFormInfo.receivableAmount" placeholder="请输入应收账款金额" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="应收账款到期日" name="receivableDueDate">
            <DatePicker style="width: 100%" v-model:value="baseFormInfo.receivableDueDate" value-format="x" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="应收账款期限（个月）" name="receivableTerm">
            <Input v-model:value="baseFormInfo.receivableTerm" placeholder="请输入应收账款期限" />
          </FormItem>
        </Col>
        <Col :span="24">
          <FormItem label="应收账款描述" name="remarks">
            <Textarea v-model:value="baseFormInfo.remarks" :rows="4" placeholder="请输入应收账款描述" />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="基础合同" />
      <ContractTable>
        <template #toolbarTools>
          <Button class="mr-2" type="primary" @click="() => addContract(gridApiContract)">增行</Button>
        </template>

        <!-- 基础合同名称输入框 -->
        <template #contractName-input="{ row }">
          <Input v-model:value="row.contractName" placeholder="请输入合同名称" />
        </template>

        <!-- 基础合同编号输入框 -->
        <template #contractCode-input="{ row }">
          <Input v-model:value="row.contractCode" placeholder="请输入合同编号" />
        </template>

        <!-- 基础合同类型选择器 -->
        <template #contractType-select="{ row }">
          <Select
            v-model:value="row.contractType"
            :options="dictStore.getDictList('RECEIVABLE_CONTRACT_TYPE')"
            placeholder="请选择合同类型"
          />
        </template>

        <!-- 金额相关输入框 -->
        <template #totalAmount-input="{ row }">
          <InputNumber
            style="width: 100%"
            v-model:value="row.totalAmount"
            placeholder="请输入金额"
            :min="0"
            :precision="2"
          />
        </template>

        <template #unpaidAmount-input="{ row }">
          <InputNumber
            style="width: 100%"
            v-model:value="row.unpaidAmount"
            placeholder="请输入金额"
            :min="0"
            :precision="2"
          />
        </template>

        <template #transferAmount-input="{ row }">
          <InputNumber v-model:value="row.transferAmount" placeholder="请输入金额" :min="0" :precision="2" />
        </template>

        <!-- 凭证相关输入框 -->
        <template #voucherName-input="{ row }">
          <Input v-model:value="row.voucherName" placeholder="请输入凭证名称" />
        </template>

        <template #voucherNumber-input="{ row }">
          <Input v-model:value="row.voucherNumber" placeholder="请输入凭证号码" />
        </template>

        <!-- 签署日期选择器 -->
        <template #signedDate-date="{ row }">
          <DatePicker v-model:value="row.signedDate" value-format="x" placeholder="请选择签署日期" />
        </template>

        <!-- 上传附件按钮 -->
        <template #upLoadFile="{ row }">
          <Button type="link" @click="() => uploadFile(row)">上传</Button>
        </template>

        <!-- 操作按钮 -->
        <template #action="{ rowIndex }">
          <TypographyLink type="danger" @click="removeContract(rowIndex)"> 删除 </TypographyLink>
        </template>
      </ContractTable>

      <BasicCaption content="发票" />
      <InvoiceTable>
        <template #toolbarTools>
          <BaseFilePickList class="fileBtn" v-loading="loading" v-model="invoiceFile" btn-text="上传发票" />
          <ImportData
            title="Excel批量上传"
            style="margin-right: 10px"
            :upload-api="invoiceImportApi"
            :download-template-api="invoiceDownloadTemplateApi"
            @import-success="handleImportSuccess"
          />
          <Button class="mr-2" type="primary" @click="() => addInvoice(gridApiInvoice)">增行</Button>
        </template>
        <!-- 发票类型输入框 -->
        <template #invoiceType-input="{ row }">
          <Select
            :disabled="row.verifyResult === '1'"
            v-model:value="row.invoiceType"
            :options="dictStore.getDictList('RECEIVABLE_INVOICE_TYPE')"
            placeholder="请选择发票类型"
          />
        </template>

        <!-- 发票号码输入框 -->
        <template #invoiceNumber-input="{ row }">
          <Input :disabled="row.verifyResult === '1'" v-model:value="row.invoiceNumber" placeholder="请输入发票号码" />
        </template>

        <!-- 发票代码输入框 -->
        <template #invoiceCode-input="{ row }">
          <Input :disabled="row.verifyResult === '1'" v-model:value="row.invoiceCode" placeholder="请输入发票代码" />
        </template>

        <!-- 金额相关输入框 -->
        <template #totalAmount-input="{ row }">
          <InputNumber
            :disabled="row.verifyResult === '1'"
            v-model:value="row.totalAmount"
            placeholder="请输入金额"
            :min="0"
            :precision="2"
          />
        </template>

        <template #totalAmountTax-input="{ row }">
          <InputNumber
            :disabled="row.verifyResult === '1'"
            v-model:value="row.totalAmountTax"
            placeholder="请输入金额"
            :min="0"
            :precision="2"
          />
        </template>

        <!-- 开票日期选择器 -->
        <template #invoiceDate-input="{ row }">
          <DatePicker
            :disabled="row.verifyResult === '1'"
            v-model:value="row.invoiceDate"
            value-format="x"
            placeholder="请选择开票日期"
          />
        </template>

        <!-- 购买方/销售方输入框 -->
        <template #buyerName-input="{ row }">
          <Input :disabled="row.verifyResult === '1'" v-model:value="row.buyerName" placeholder="请输入购买方" />
        </template>

        <template #sellerName-input="{ row }">
          <Input :disabled="row.verifyResult === '1'" v-model:value="row.sellerName" placeholder="请输入销售方" />
        </template>

        <!-- 校验码输入框 -->
        <template #checkCode-input="{ row }">
          <Input :disabled="row.verifyResult === '1'" v-model:value="row.checkCode" placeholder="请输入校验码后6位" />
        </template>

        <!-- 上传附件按钮 -->
        <!-- <template #upLoadFile="{ row }">
          <Button type="link" @click="() => uploadFile(row)">上传</Button>
        </template> -->

        <!-- 操作按钮 -->
        <template #action="{ row, rowIndex }">
          <TypographyLink v-if="row.verifyResult !== '1'" type="primary" @click="() => checkInvoice(row)">
            验真
          </TypographyLink>

          <TypographyLink type="danger" @click="removeInvoice(rowIndex)"> 删除 </TypographyLink>
        </template>
      </InvoiceTable>
      <BaseAttachmentList
        v-model="baseFormInfo.attachmentList"
        :business-id="baseFormInfo.id"
        business-type="FCT_RECEIVABLE"
        edit-mode
      >
        <template #header>
          <TypographyText>应收账款佐证材料</TypographyText>
        </template>
      </BaseAttachmentList>
    </Form>
  </BasicPopup>
</template>

<style scoped>
:deep(.fileBtn) div {
  display: none !important;
}

.fileBtn {
  margin-right: 10px;

  .mb-1 {
    margin-bottom: 0;
  }
}
</style>
