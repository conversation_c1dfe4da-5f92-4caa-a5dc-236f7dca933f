<script setup lang="ts">
import { ref, watch } from 'vue';

import { DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { BaseAttachmentList } from '#/adapter/base-ui';
// 定义props，接收外部传入的initiationForm
const props = defineProps({
  form: {
    type: Object,
    default: () => ({}),
  },
});

// 创建一个响应式变量来存储处理后的数据
const initiationForm = ref({});

const dictStore = useDictStore();
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};

// 处理关联企业数据
const processCompanyList = (companyList: any[]) => {
  const processed = { ...initiationForm.value };

  // 提取担保企业
  const guarantorCompany = companyList.find((item: any) => item.projectCompanyType === 'guarantee');
  processed.guarantor = guarantorCompany?.companyName || '';

  // 提取担保企业
  const creditCompany = companyList.find((item: any) => item.projectCompanyType === 'credit');
  processed.credit = creditCompany?.companyName || '';

  // 提取用信企业（支持多选）
  const creditUser = companyList
    .filter((item: any) => item.projectCompanyType === 'using')
    .map((item: any) => item.companyName || '');
  processed.creditUser = creditUser.join(',');

  return processed;
};

// 处理相关人员数据
const processUserList = (userList: any[]) => {
  const processed = { ...initiationForm.value };

  // 提取业务经理
  const businessUser = userList.find((item: any) => item.projectUserType === 'business');
  processed.businessManager = businessUser?.userName?.toString() || '';

  // 提取运营经理
  const operationsUser = userList.find((item: any) => item.projectUserType === 'operations');
  processed.operationsManager = operationsUser?.userName?.toString() || '';

  // 提取风控经理
  const riskUser = userList.find((item: any) => item.projectUserType === 'risk');
  processed.riskControlManager = riskUser?.userName?.toString() || '';

  // 提取财务经理
  const financeUser = userList.find((item: any) => item.projectUserType === 'finance');
  processed.financialManager = financeUser?.userName?.toString() || '';

  return processed;
};

// 监听整个initiationForm的变化，用于初始化
watch(
  () => props.form,
  (newVal) => {
    initiationForm.value = { ...newVal };
    // 当整个对象变化时，重新处理所有数据
    if (newVal.companyList) {
      initiationForm.value = processCompanyList(newVal.companyList);
    }
    if (newVal.userList) {
      initiationForm.value = processUserList(newVal.userList);
    }
  },
  { immediate: true, deep: true },
);
</script>

<template>
  <div>
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="项目类型">
        {{ dictStore.formatter(initiationForm.projectType, 'FCT_PROJECT_TYPE') }}
      </a-descriptions-item>
      <a-descriptions-item label="合作企业">
        {{ initiationForm.targetCompanyName }}
      </a-descriptions-item>
      <a-descriptions-item label="地市">
        {{ initiationForm.cityName }}
      </a-descriptions-item>
      <a-descriptions-item label="区县">
        {{ initiationForm.districtName }}
      </a-descriptions-item>
      <a-descriptions-item label="项目名称">
        {{ initiationForm.projectName }}
      </a-descriptions-item>
      <a-descriptions-item label="项目编号">
        {{ initiationForm.projectCode }}
      </a-descriptions-item>
    </a-descriptions>

    <!-- 项目背景 -->
    <BasicCaption content="项目背景" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="项目背景描述" :span="2">
        {{ initiationForm.backgroundDesc }}
      </a-descriptions-item>
    </a-descriptions>

    <!-- 交易主体信息 -->
    <BasicCaption content="交易主体信息" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="授信对象">
        {{ initiationForm.credit }}
      </a-descriptions-item>
      <a-descriptions-item label="担保人">
        {{ initiationForm.guarantor }}
      </a-descriptions-item>
      <a-descriptions-item label="授信对象基本情况" :span="2">
        {{ initiationForm.creditRecipientDesc }}
      </a-descriptions-item>
      <a-descriptions-item label="担保人基本情况" :span="2">
        {{ initiationForm.guarantorInfoDesc }}
      </a-descriptions-item>
    </a-descriptions>

    <!-- 授信方案 -->
    <BasicCaption content="授信方案" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="客户测算额度（元）">
        {{ initiationForm.creditCalculateAmount }}
      </a-descriptions-item>
      <a-descriptions-item label="授信额度（元）">
        {{ initiationForm.creditAmount }}
      </a-descriptions-item>
      <a-descriptions-item label="单笔用信额度上限（元）">
        {{ initiationForm.creditSingleMaxUsed }}
      </a-descriptions-item>
      <a-descriptions-item label="授信期限（个月）">
        {{ initiationForm.creditTerm }}
      </a-descriptions-item>
      <a-descriptions-item label="是否为续授信">
        {{ dictStore.formatter(initiationForm.continuationCreditFlag, 'baseBooleanType') }}
      </a-descriptions-item>
      <a-descriptions-item label="授信费率（%）">
        {{ initiationForm.creditRate }}
      </a-descriptions-item>
      <a-descriptions-item label="用信主体">
        {{ initiationForm.creditUser }}
      </a-descriptions-item>
      <a-descriptions-item label="最低用信期限（个月）">
        {{ initiationForm.creditMinPeriod }}
      </a-descriptions-item>
      <a-descriptions-item label="授信额度类型">
        {{ dictStore.formatter(initiationForm.creditType, 'FCT_CREDIT_TYPE') }}
      </a-descriptions-item>
      <a-descriptions-item label="增信措施" :span="2">
        {{ initiationForm.creditEnhancementDesc }}
      </a-descriptions-item>
      <a-descriptions-item label="资金用途" :span="2">
        {{ initiationForm.fundUsageDesc }}
      </a-descriptions-item>
    </a-descriptions>

    <!-- 项目负责人 -->
    <BasicCaption content="项目负责人" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="业务经理">
        {{ initiationForm.businessManager }}
      </a-descriptions-item>
      <a-descriptions-item label="风控经理">
        {{ initiationForm.riskControlManager }}
      </a-descriptions-item>
      <a-descriptions-item label="运营经理">
        {{ initiationForm.operationsManager }}
      </a-descriptions-item>
      <a-descriptions-item label="财务经理">
        {{ initiationForm.financialManager }}
      </a-descriptions-item>
    </a-descriptions>

    <BasicCaption content="其他情况说明" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="其他情况说明" :span="2">
        {{ initiationForm.creditOtherDesc }}
      </a-descriptions-item>
    </a-descriptions>
    <BaseAttachmentList
      v-if="initiationForm.businessTypeFile"
      :business-id="initiationForm.id"
      :business-type="initiationForm.businessTypeFile"
    />
  </div>
</template>
