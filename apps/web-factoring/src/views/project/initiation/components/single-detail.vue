<script setup lang="ts">
import type { ProjectCompanyBO, ProjectUserBO } from '#/api';

import { ref, watch } from 'vue';

import { DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { BaseAttachmentList } from '#/adapter/base-ui';
import AccountsReceivableDetail from '#/views/project/components/accounts-receivable-detail.vue';
import AccountsReceivablePoolDetail from '#/views/project/components/accounts-receivable-pool-detail.vue';
import MortgageDetail from '#/views/project/components/mortgage-detail.vue';
import PledgeDetail from '#/views/project/components/pledge-detail.vue';
// 定义接收的props
const props = defineProps({
  form: {
    type: Object,
    default: () => ({}),
  },
});

// 创建响应式变量存储处理后的数据
const initiationForm = ref({});

const dictStore = useDictStore();
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};

// 处理关联企业数据的纯函数
const processCompanyList = (companyList: ProjectCompanyBO) => {
  const processed = { ...initiationForm.value };

  // 提取担保企业
  const guarantorCompany = companyList.find((item: any) => item.projectCompanyType === 'guarantee');
  processed.guarantor = guarantorCompany?.companyName || '';

  // 提取债权人
  const creditorCompany = companyList.find((item: any) => item.projectCompanyType === 'creditor');
  processed.creditor = creditorCompany?.companyName || '';

  // 提取债务人
  const debtorCompany = companyList.find((item: any) => item.projectCompanyType === 'debtor');
  processed.debtor = debtorCompany?.companyName || '';

  return processed;
};

// 处理相关人员数据的纯函数
const processUserList = (userList: ProjectUserBO) => {
  const processed = { ...initiationForm.value };

  // 提取业务经理
  const businessUser = userList.find((item: any) => item.projectUserType === 'business');
  processed.businessManager = businessUser?.userName?.toString() || '';

  // 提取运营经理
  const operationsUser = userList.find((item: any) => item.projectUserType === 'operations');
  processed.operationsManager = operationsUser?.userName?.toString() || '';

  // 提取风控经理
  const riskUser = userList.find((item: any) => item.projectUserType === 'risk');
  processed.riskControlManager = riskUser?.userName?.toString() || '';

  // 提取财务经理
  const financeUser = userList.find((item: any) => item.projectUserType === 'finance');
  processed.financialManager = financeUser?.userName?.toString() || '';

  return processed;
};

// 监听整个initiationForm变化
watch(
  () => props.form,
  (newVal) => {
    initiationForm.value = { ...newVal };
    // 当整个对象变化时重新处理所有数据
    if (newVal.companyList) {
      initiationForm.value = processCompanyList(newVal.companyList);
    }
    if (newVal.userList) {
      initiationForm.value = processUserList(newVal.userList);
    }
  },
  { immediate: true, deep: true },
);
</script>

<template>
  <div>
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="项目类型">
        {{ dictStore.formatter(initiationForm.projectType, 'FCT_PROJECT_TYPE') }}
      </a-descriptions-item>
      <a-descriptions-item label="投向行业">
        {{ dictStore.formatter(initiationForm.targetIndustry, 'FCT_TARGET_INDUSTRY') }}
      </a-descriptions-item>
      <a-descriptions-item label="合作企业">
        {{ initiationForm.targetCompanyName }}
      </a-descriptions-item>
      <a-descriptions-item label="地市">
        {{ initiationForm.cityName }}
      </a-descriptions-item>
      <a-descriptions-item label="区县">
        {{ initiationForm.districtName }}
      </a-descriptions-item>
      <a-descriptions-item label="是否支持实体经济">
        {{ dictStore.formatter(initiationForm.isSupportRealEconomy, 'baseBooleanType') }}
      </a-descriptions-item>
      <a-descriptions-item label="是否为省重点产业">
        {{ dictStore.formatter(initiationForm.isProvincialKeyIndustry, 'baseBooleanType') }}
      </a-descriptions-item>
      <a-descriptions-item label="项目名称">
        {{ initiationForm.projectName }}
      </a-descriptions-item>
      <a-descriptions-item label="项目编号">
        {{ initiationForm.projectCode }}
      </a-descriptions-item>
    </a-descriptions>

    <!-- 业务类型 -->
    <BasicCaption content="业务类型" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="保理类型">
        {{ dictStore.formatter(initiationForm.factoringType, 'FCT_FACTORING_TYPE') }}
      </a-descriptions-item>
      <a-descriptions-item label="支持操作模式">
        {{ dictStore.formatter(initiationForm.supportMode, 'FCT_SUPPORT_MODE') }}
      </a-descriptions-item>
      <a-descriptions-item label="支持保理方向">
        {{ dictStore.formatter(initiationForm.factoringDirection, 'FCT_FACTORING_DIRECTION') }}
      </a-descriptions-item>
      <a-descriptions-item label="追索权要求">
        {{ dictStore.formatter(initiationForm.recourseRequired, 'FCT_RECOURES_REQUIRED') }}
      </a-descriptions-item>
    </a-descriptions>

    <!-- 项目背景 -->
    <BasicCaption content="项目背景" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="项目背景" :span="2">
        {{ initiationForm.backgroundDesc }}
      </a-descriptions-item>
    </a-descriptions>

    <!-- 交易主体信息 -->
    <BasicCaption content="交易主体信息" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="债权人">
        {{ initiationForm.creditor }}
      </a-descriptions-item>
      <a-descriptions-item label="担保人">
        {{ initiationForm.guarantor }}
      </a-descriptions-item>
      <a-descriptions-item label="债务人">
        {{ initiationForm.debtor }}
      </a-descriptions-item>
      <a-descriptions-item label="债权人基本情况" :span="2">
        {{ initiationForm.creditorInfoDesc }}
      </a-descriptions-item>
      <a-descriptions-item label="债务人基本情况" :span="2">
        {{ initiationForm.debtorInfoDesc }}
      </a-descriptions-item>
      <a-descriptions-item label="担保人基本情况" :span="2">
        {{ initiationForm.guarantorInfoDesc }}
      </a-descriptions-item>
    </a-descriptions>
    <component
      :is="initiationForm.factoringType !== 'pool_factoring' ? AccountsReceivableDetail : AccountsReceivablePoolDetail"
      :info="initiationForm"
    />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="应收账款描述" :span="2">
        {{ initiationForm.receivableDesc }}
      </a-descriptions-item>
    </a-descriptions>
    <!-- 交易方案 -->
    <BasicCaption content="交易方案" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="保理融资金额（元）">
        {{ initiationForm.financingAmount }}
      </a-descriptions-item>
      <a-descriptions-item label="保理融资比例（%）">
        {{ initiationForm.financingRatio }}
      </a-descriptions-item>
      <a-descriptions-item label="预估综合收益率（%）">
        {{ initiationForm.estimateIncomeRate }}
      </a-descriptions-item>
      <a-descriptions-item label="服务费（元）">
        {{ initiationForm.serviceAmount }}
      </a-descriptions-item>
      <a-descriptions-item label="授信额度类型">
        {{ dictStore.formatter(initiationForm.creditType, 'FCT_CREDIT_TYPE') }}
      </a-descriptions-item>
      <a-descriptions-item label="增信措施描述" :span="2">
        {{ initiationForm.creditEnhancementDesc }}
      </a-descriptions-item>
      <a-descriptions-item label="风控措施描述" :span="2">
        {{ initiationForm.riskControlDesc }}
      </a-descriptions-item>
      <a-descriptions-item label="资金用途" :span="2">
        {{ initiationForm.fundUsageDesc }}
      </a-descriptions-item>
    </a-descriptions>
    <MortgageDetail :info="initiationForm" />
    <PledgeDetail :info="initiationForm" />
    <!-- 额度说明 -->
    <BasicCaption content="额度说明" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="额度说明" :span="2">
        {{ initiationForm.creditLimitDesc }}
      </a-descriptions-item>
    </a-descriptions>

    <!-- 项目负责人 -->
    <BasicCaption content="项目负责人" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="业务经理">
        {{ initiationForm.businessManager }}
      </a-descriptions-item>
      <a-descriptions-item label="风控经理">
        {{ initiationForm.riskControlManager }}
      </a-descriptions-item>
      <a-descriptions-item label="运营经理">
        {{ initiationForm.operationsManager }}
      </a-descriptions-item>
      <a-descriptions-item label="财务经理">
        {{ initiationForm.financialManager }}
      </a-descriptions-item>
    </a-descriptions>

    <!-- 其他情况说明 -->
    <BasicCaption content="其他情况说明" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="其他情况说明" :span="2">
        {{ initiationForm.creditOtherDesc }}
      </a-descriptions-item>
    </a-descriptions>
    <BaseAttachmentList
      v-if="initiationForm.businessTypeFile"
      :business-id="initiationForm.id"
      :business-type="initiationForm.businessTypeFile"
    />
  </div>
</template>
