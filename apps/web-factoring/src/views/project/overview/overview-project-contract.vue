<script setup lang="ts">
import type { ReceivableVO } from '#/api';

import { computed, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { Button } from 'ant-design-vue';

import {} from '#/api';
import { ContractList } from '#/components';

const emit = defineEmits(['register', 'ok']);

const contractList = ref();
const id = ref();

const title = computed(() => {
  return '新增';
});

// 添加初始化方法
const init = (data: ReceivableVO) => {
  if (data && Object.keys(data).length > 0) {
    id.value = data.projectId;
  }
};

const [registerPopup, { changeOkLoading }] = usePopupInner((data) => init(data));
const handleSave = async () => {
  try {
    const params = {
      contractList: contractList.value,
      projectId: id.value
    };
    emit('ok', params);
  } catch (error) {
    console.error('提交失败:', error);
  } finally {
    changeOkLoading(false);
  }
};

const submit = async () => {
  handleSave();
};
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup" >
    <template #centerToolbar>
      <Button class="ml-2" type="primary" @click="submit">提交</Button>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <ContractList v-model="contractList" :business-id="id" business-type="FCT_PROJECT_CONTRACT" edit-mode />
    </div>
  </BasicPopup>
</template>

<style scoped>
:deep(.fileBtn) div {
  display: none !important;
}

.fileBtn {
  margin-right: 10px;

  .mb-1 {
    margin-bottom: 0;
  }
}
</style>
