<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { OverviewInfo, ProjectUserBO } from '#/api';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { message } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  bindingProjectByContract,
  delOverviewApi,
  getChangeStatusApi,
  getOverviewInfoApi,
  getOverviewPageListApi,
  getUserListApi,
  transferUserProject,
} from '#/api';
import OverviewChange from '#/views/project/overview/overview-change.vue';
import OverviewDetail from '#/views/project/overview/overview-detail.vue';
import OverviewProjectContract from '#/views/project/overview/overview-project-contract.vue';

const dictStore = useDictStore();

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '项目编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Select',
      fieldName: 'projectType',
      label: '项目类型',
      componentProps: {
        options: dictStore.getDictList('FCT_PROJECT_TYPE'),
      },
    },
    {
      component: 'Input',
      fieldName: 'targetCompanyName',
      label: '合作企业',
    },
    {
      component: 'Select',
      fieldName: 'survivalStatus',
      label: '存续状态',
      componentProps: {
        options: dictStore.getDictList('FCT_SURVIVAL_STATUS'),
      },
    },
    {
      component: 'Input',
      fieldName: 'businessUserNames',
      label: '业务经理',
    },
    {
      component: 'Input',
      fieldName: 'operationsUserNames',
      label: '运营经理',
    },
    {
      component: 'Input',
      fieldName: 'riskUserNames',
      label: '风控经理',
    },
    {
      component: 'Input',
      fieldName: 'financeUserNames',
      label: '财务经理',
    },
    {
      component: 'Input',
      fieldName: 'cityName',
      label: '地市',
    },
    {
      component: 'Input',
      fieldName: 'districtName',
      label: '区县',
    },
  ],
  commonConfig: {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'projectCode', title: '项目编号', minWidth: 200 },
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    {
      field: 'projectType',
      title: '项目类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_PROJECT_TYPE',
        },
      },
    },
    { field: 'cityName', title: '地市', minWidth: 120 },
    { field: 'districtName', title: '区县', minWidth: 120 },
    { field: 'targetCompanyName', title: '合作企业', minWidth: 160 },
    { field: 'businessUserNames', title: '业务经理', minWidth: 120 },
    { field: 'operationsUserNames', title: '运营经理', minWidth: 120 },
    { field: 'riskUserNames', title: '风控经理', minWidth: 120 },
    { field: 'financeUserNames', title: '财务经理', minWidth: 120 },
    {
      field: 'meetingReviewStatus',
      title: '项目评审会状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_MEETING_STATUS',
        },
      },
      minWidth: 120,
    },
    {
      field: 'meetingBranchStatus',
      title: '党支部会议状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_MEETING_STATUS',
        },
      },
      minWidth: 120,
    },
    {
      field: 'meetingGeneralStatus',
      title: '总经办会议状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_MEETING_STATUS',
        },
      },
      minWidth: 120,
    },
    {
      field: 'survivalStatus',
      title: '存续状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_SURVIVAL_STATUS',
        },
      },
      minWidth: 120,
    },
    {
      field: 'status',
      title: '操作状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_STATUS',
        },
      },
      minWidth: 120,
    },
    {
      field: 'reviewStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
      minWidth: 120,
    },
    {
      field: 'contractReviewStatus',
      title: '合同状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
      minWidth: 120,
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 'auto',
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getOverviewPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: OverviewInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});
const edit = (row: OverviewInfo) => {
  openFormPopup(true, row);
};
const change = async (row: OverviewInfo) => {
  await getChangeStatusApi(row.id as number);
  const data = { ...row };
  openFormPopup(true, data);
};
const editSuccess = () => {
  gridApi.reload();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerProjectContract, { openPopup: openProjectContractPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: OverviewInfo) => {
  openDetailPopup(true, row);
};

const ProjectFormRef = ref();
const add = async () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择要移交的项目');
    return false;
  }
  projectForm.value = await getOverviewInfoApi(res[0].id as number);
  initUserList();
  modalApi.open();
};
const userOptions = ref<{ userId: number; userName: string }[]>([]);

const loadUserOptions = async () => {
  userOptions.value = await getUserListApi();
};

loadUserOptions();

const filterOption = (input: string, option: any) => {
  return option.realName.toLowerCase().includes(input.toLowerCase());
};

// 获取人员名称
const getUserLabel = (userId: string): string => {
  if (!userId) return '';

  // 从缓存中查找
  const user = userOptions.value.find((item) => item.id === userId);

  return user?.realName || '';
};

// 保存数据时转换相关人员数据
const saveUserList = (): ProjectUserBO[] => {
  const userList: ProjectUserBO[] = [];

  // 添加业务经理
  if (projectForm.value.businessManager) {
    userList.push({
      userId: projectForm.value.businessManager,
      userName: getUserLabel(projectForm.value.businessManager),
      projectUserType: 'business',
    });
  }

  // 添加运营经理
  if (projectForm.value.operationsManager) {
    userList.push({
      userId: projectForm.value.operationsManager,
      userName: getUserLabel(projectForm.value.operationsManager),
      projectUserType: 'operations',
    });
  }

  // 添加风控经理
  if (projectForm.value.riskControlManager) {
    userList.push({
      userId: projectForm.value.riskControlManager,
      userName: getUserLabel(projectForm.value.riskControlManager),
      projectUserType: 'risk',
    });
  }

  // 添加财务经理
  if (projectForm.value.financialManager) {
    userList.push({
      userId: projectForm.value.financialManager,
      userName: getUserLabel(projectForm.value.financialManager),
      projectUserType: 'finance',
    });
  }

  return userList;
};
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await ProjectFormRef.value.validate();
    projectForm.value.userList = saveUserList();
    await transferUserProject(projectForm.value);
    message.success($t('base.resSuccess'));
    await modalApi.close();
    await gridApi.reload();
  },
  onClosed: () => {
    ProjectFormRef.value.resetFields();
    projectForm.value = {};
  },
});
const projectForm = ref<OverviewInfo>({});
const rules = {};
// 初始化相关人员数据
const initUserList = () => {
  const userList = projectForm.value.userList || [];

  // 提取业务经理
  const businessUser = userList.find((item: object) => item.projectUserType === 'business');
  projectForm.value.businessManager = businessUser?.userId;

  // 提取运营经理
  const operationsUser = userList.find((item: object) => item.projectUserType === 'operations');
  projectForm.value.operationsManager = operationsUser?.userId;

  // 提取风控经理
  const riskUser = userList.find((item: object) => item.projectUserType === 'risk');
  projectForm.value.riskControlManager = riskUser?.userId;

  // 提取财务经理
  const financeUser = userList.find((item: object) => item.projectUserType === 'finance');
  projectForm.value.financialManager = financeUser?.userId;
};
const del = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const id = res[0].id;
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该项目，是否继续？',
    async onOk() {
      await delOverviewApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

const viewProjectContract = (row: OverviewInfo) => {
  openProjectContractPopup(true, row);
};
const editContractSuccess = async (params: any) => {
  try {
    await bindingProjectByContract(params);
    message.success($t('base.resSuccess'));
    openFormPopup(false, {});
    await gridApi.formApi.submitForm(); // 刷新列表数据
  } catch {}
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="add"> 项目移交 </a-button>
        </a-space>
        <a-button type="primary" danger @click="del">
          {{ $t('base.del') }}
        </a-button>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['SUBMIT', 'EDITING'].includes(row.status)" @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
          <a-typography-link
            v-if="!['settled'].includes(row.survivalStatus) && ['EFFECTIVE'].includes(row.status)"
            @click="change(row)"
          >
            变更
          </a-typography-link>
          <a-typography-link @click="viewProjectContract(row)"> 项目合同 </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <OverviewChange @register="registerForm" @ok="editSuccess" />
    <OverviewDetail @register="registerDetail" />
    <OverviewProjectContract @register="registerProjectContract" @ok="editContractSuccess" />
    <Modal title="选择项目">
      <a-form ref="ProjectFormRef" :model="projectForm" :rules="rules">
        <a-form-item label="业务经理" name="businessManager">
          <a-select
            v-model:value="projectForm.businessManager"
            show-search
            :options="userOptions"
            :field-names="{ label: 'realName', value: 'id' }"
            :filter-option="filterOption"
          />
        </a-form-item>
        <a-form-item label="风控经理" name="riskControlManager">
          <a-select
            v-model:value="projectForm.riskControlManager"
            show-search
            :options="userOptions"
            :field-names="{ label: 'realName', value: 'id' }"
            :filter-option="filterOption"
          />
        </a-form-item>
        <a-form-item label="运营经理" name="operationsManager">
          <a-select
            v-model:value="projectForm.operationsManager"
            show-search
            :options="userOptions"
            :field-names="{ label: 'realName', value: 'id' }"
            :filter-option="filterOption"
          />
        </a-form-item>
        <a-form-item label="财务经理" name="financialManager">
          <a-select
            v-model:value="projectForm.financialManager"
            show-search
            :options="userOptions"
            :field-names="{ label: 'realName', value: 'id' }"
            :filter-option="filterOption"
          />
        </a-form-item>
      </a-form>
    </Modal>
  </Page>
</template>

<style></style>
