<script setup lang="ts">
import type { OverviewInfo, PricingInfo } from '#/api';

import { nextTick, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { omit } from 'lodash-es';
import { ContractList } from '#/components';
import { BaseAttachmentList } from '#/adapter/base-ui';
import { getOverviewInfoApi, getPricingInfoApi, getOverviewContractListApi } from '#/api';
import DebtServiceDetail from '#/views/project/components/debt-service-detail.vue';
import RepaymentCalculationDetail from '#/views/project/components/repayment-calculation-detail.vue';
import RepaymentCalculationHistory from '#/views/project/components/repayment-calculation-history.vue';
import ComprehensiveDetail from '#/views/project/initiation/components/comprehensive-detail.vue';
import SingleDetail from '#/views/project/initiation/components/single-detail.vue';
import ChangeRecord from '#/views/project/overview/components/change-record.vue';
import BaseDetail from '#/views/project/pricing/components/base-detail.vue';
import PricingScheme from '#/views/project/pricing/components/pricing-scheme.vue';

const initiationForm = ref<OverviewInfo>({});
const ChangeRecordRef = ref();
const init = async (data: OverviewInfo) => {
  activeKey.value = 'initiation';
  const info = data?.id ? await getOverviewInfoApi(data.id as number) : data;
  info.businessTypeFile = 'FCT_PROJECT';
  initiationForm.value = info;
  if (initiationForm.value.pricingId) {
    await getPricingInfo(initiationForm.value);
  }
};
const contractList = ref();
const changeTab = (key: string) => {
  if (key === 'changeRecord') {
    nextTick(() => {
      ChangeRecordRef.value.init(initiationForm.value);
    });
  }
  if (key === 'pricing' && pricingForm.value.projectType === 'single') {
    nextTick(() => {
      RepaymentCalculationHistoryRef.value.init(pricingForm.value);
    });
  }
  if (key === 'contract') {
    nextTick(async () => {
      const param = {
        bizType: 'FCT_PROJECT_CONTRACT',
        bizId: initiationForm.value.id
      }
      const res = await getOverviewContractListApi(param)
      contractList.value = res.data
    });
  }
};
const pricingForm = ref<OverviewInfo>({});
const getPricingInfo = async (data: PricingInfo) => {
  let info = data.id ? await getPricingInfoApi(data.pricingId as number) : data;
  if (info.projectType === 'single') {
    const calculation = omit(
      info.calculation,
      'id',
      'targetCompanyName',
      'targetCompanyCode',
      'projectCode',
      'projectName',
    );
    info = {
      ...info,
      ...calculation,
    };
  }
  pricingForm.value = info;
};
const RepaymentCalculationHistoryRef = ref();
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const activeKey = ref('initiation');
const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" :show-ok-btn="false" title="项目总览" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-tabs v-model:active-key="activeKey" @change="changeTab">
        <a-tab-pane key="initiation" tab="立项信息">
          <ComprehensiveDetail v-show="initiationForm.projectType === 'comprehensive'" :form="initiationForm" />
          <SingleDetail v-show="initiationForm.projectType === 'single'" :form="initiationForm" />
        </a-tab-pane>
        <a-tab-pane key="pricing" tab="项目定价" v-if="initiationForm.pricingId">
          <BaseDetail :pricing-form="pricingForm" :descriptions-prop="descriptionsProp" />
          <PricingScheme :pricing-form="pricingForm" :descriptions-prop="descriptionsProp" />
          <div v-show="pricingForm.projectType === 'single'">
            <DebtServiceDetail :debt-service-form="pricingForm" :descriptions-prop="descriptionsProp" />
            <RepaymentCalculationDetail :calculation-form="pricingForm" :descriptions-prop="descriptionsProp"
              calculation-type="Pricing" />
            <RepaymentCalculationHistory ref="RepaymentCalculationHistoryRef" calculation-type="Pricing" />
          </div>
          <BaseAttachmentList :business-id="pricingForm.id" business-type="FCT_PROJECT_PRICING" />
        </a-tab-pane>
        <a-tab-pane key="changeRecord" tab="变更记录">
          <ChangeRecord ref="ChangeRecordRef" />
        </a-tab-pane>
        <a-tab-pane key="contract" tab="合同信息">
          <ContractList v-model="contractList" :business-id="initiationForm.id" business-type="FCT_PROJECT_CONTRACT" />
        </a-tab-pane>
      </a-tabs>
    </div>
  </BasicPopup>
</template>

<style></style>
