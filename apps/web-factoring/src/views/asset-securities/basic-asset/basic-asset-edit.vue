<script setup lang="ts">
import type { BasicAssetInfo } from '#/api';

import { reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { message } from 'ant-design-vue';
import { cloneDeep, omit } from 'lodash-es';

import {
  addBasicAssetApi,
  editBasicAssetApi,
  getAssetProjectIdInfoApi,
  getBasicAssetInfoApi,
  getProjectListApi,
} from '#/api';
import AccountsReceivable from '#/views/asset-securities/basic-asset/components/accounts-receivable.vue';

const emit = defineEmits(['ok', 'register']);

const init = async (data: BasicAssetInfo) => {
  basicForm.value = {};
  if (data.id) {
    const info = data.id ? await getBasicAssetInfoApi(data.id as number) : data;
    basicForm.value = { ...basicForm.value, ...info };
  }
};
const projectOptions = ref<any[]>([]);

const getProjectList = async () => {
  projectOptions.value = await getProjectListApi({ status: 'EFFECTIVE', isMeetingCompleted: 1 });
};
getProjectList();
const getReceivableDetail = async () => {
  const data = await getAssetProjectIdInfoApi(basicForm.value.projectId);
  const info = omit(data, 'id', 'absAssetCode');
  basicForm.value = { ...basicForm.value, ...info };
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const FormRef = ref();
const basicForm = ref<BasicAssetInfo>({});

const loading = reactive({
  submit: false,
});
const save = async (type: string) => {
  await FormRef.value.validate();
  let api = addBasicAssetApi;
  if (basicForm.value.id) {
    api = editBasicAssetApi;
  }
  const formData = cloneDeep(basicForm.value);
  loading.submit = true;
  if (type === 'submit') formData.isSubmit = true;
  try {
    const res = await api(formData as BasicAssetInfo);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};
const rules = {
  projectId: [{ required: true, message: '请选择关联融资项目', trigger: 'change' }],
  factoringContractCode: [{ required: true, message: '请输入保理合同编号' }],
  absAssetName: [{ required: true, message: '请输入ABS基础资产名称' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 6 }, wrapperCol: { span: 18 } };
const colSpan = COL_SPAN_PROP;
</script>

<template>
  <BasicPopup v-bind="$attrs" title="ABS基础资产信息" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save('save')">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="basicForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="关联融资项目" name="projectId">
              <a-select
                v-model:value="basicForm.projectId"
                :options="projectOptions"
                :field-names="{ label: 'projectName', value: 'id' }"
                @change="getReceivableDetail"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="ABS基础资产编号"> {{ basicForm.absAssetCode }} </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="保理合同编号" name="factoringContractCode">
              <a-input v-model:value="basicForm.factoringContractCode" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="ABS基础资产名称" name="absAssetName">
              <a-input v-model:value="basicForm.absAssetName" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="保理融资金额（元）"> {{ basicForm.financingAmount }} </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="应收账款总金额（元）"> {{ basicForm.receivableAmount }} </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="融资利率（%）"> {{ basicForm.financingRate }} </a-form-item>
          </a-col>
        </a-row>
        <AccountsReceivable :receivable-form="basicForm" />
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
