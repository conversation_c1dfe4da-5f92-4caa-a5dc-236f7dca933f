<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { AssetPoolInfo } from '#/api';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { Modal as AntdModal, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { delAssetPoolApi, getAssetPoolPageListApi } from '#/api';

import AssetPoolDetail from './asset-pool-detail.vue';
import AssetPoolEdit from './asset-pool-edit.vue';

const dictStore = useDictStore();

// 搜索表单配置（按原型字段调整）
const formOptions = defineFormOptions({
  schema: [
    { component: 'Input', fieldName: 'assetPoolName', label: 'ABS资产池名称' },
    { component: 'Input', fieldName: 'absProjectName', label: 'ABS项目名称' },
    {
      component: 'Select',
      fieldName: 'statusList',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList('FCT_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'packageStatusList',
      label: '封包状态',
      componentProps: {
        options: dictStore.getDictList('FCT_ABS_POOL_PACKAGE_STATUS'),
      },
    },
  ],
  commonConfig: { labelCol: { span: 6 }, wrapperCol: { span: 18 } },
});

// 表格列配置（按原型顺序 & 接口字段映射）
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'assetPoolName', title: 'ABS资产池名称', minWidth: 200 },
    { field: 'absProjectName', title: 'ABS项目名称', minWidth: 200 },
    { field: 'assetAmount', title: '包内资产总额（元）', minWidth: 150 },
    { field: 'assetCount', title: '包内资产笔数', minWidth: 150 },
    { field: 'packageDate', title: '封包日期', minWidth: 150 },
    {
      field: 'status',
      title: '操作状态',
      cellRender: { name: 'CellStatus', props: { code: 'FCT_STATUS' } },
      minWidth: 120,
    },
    {
      field: 'packageStatus',
      title: '封包状态',
      cellRender: { name: 'CellStatus', props: { code: 'FCT_ABS_POOL_PACKAGE_STATUS' } },
      minWidth: 120,
    },
    { field: 'action', title: '操作', fixed: 'right', width: 120, slots: { default: 'action' } },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) =>
        await getAssetPoolPageListApi({ current: page.currentPage, size: page.pageSize, ...formValues }),
    },
  },
  checkboxConfig: { showHeader: false },
  toolbarConfig: { slots: { tools: 'toolbar-tools' } },
};

const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();

// 新增
const add = () => openFormPopup(true, {});
// 编辑
const edit = (row: AssetPoolInfo) => openFormPopup(true, row);
const editSuccess = () => gridApi.reload();
// 查看详情
const viewDetail = (row: AssetPoolInfo) => openDetailPopup(true, row);
// 删除
const del = () => {
  const selected = gridApi.grid.getCheckboxRecords(true);
  if (selected.length === 0) return message.error('请选择数据');
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该ABS资产池，是否继续？',
    async onOk() {
      await delAssetPoolApi(selected[0].id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const input = () => {};
const out = () => {};
const packaged = () => {};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button type="primary" @click="add">{{ $t('base.add') }}</a-button>
          <a-button type="primary" danger @click="del">{{ $t('base.del') }}</a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['SUBMIT'].includes(row.status)" @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
          <a-typography-link @click="viewDetail(row)">{{ $t('base.detail') }}</a-typography-link>
          <a-typography-link @click="input(row)">入池</a-typography-link>
          <a-typography-link @click="out(row)">出池</a-typography-link>
          <a-typography-link @click="packaged(row)">封包</a-typography-link>
        </a-space>
      </template>
    </Grid>
    <AssetPoolEdit @register="registerForm" @ok="editSuccess" />
    <AssetPoolDetail @register="registerDetail" />
  </Page>
</template>
