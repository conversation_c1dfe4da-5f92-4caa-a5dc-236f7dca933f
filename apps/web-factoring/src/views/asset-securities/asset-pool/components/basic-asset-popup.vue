<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { useVbenModal } from '@vben/common-ui';
import { defineFormOptions } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getReceivablePageListApi } from '#/api';

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'receivableName',
      label: '应收账款名称',
    },
    {
      component: 'Input',
      fieldName: 'creditorName',
      label: '债权人',
    },
    {
      component: 'Input',
      fieldName: 'debtorName',
      label: '债务人',
    },
  ],
  showCollapseButton: false,
  submitOnEnter: true,
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'receivableName', title: '应收账款名称' },
    { field: 'creditorName', title: '债权人' },
    { field: 'debtorName', title: '债务人' },
    { field: 'receivableAmount', title: '应收账款金额（元）' },
    { field: 'receivableTerm', title: '应收账款期限（月）' },
    { field: 'receivableDueDate', title: '应收账款到期日' },
  ],
  height: 500,
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getReceivablePageListApi({
          ...formValues,
          statusList: 'EFFECTIVE',
          current: page.currentPage,
          size: page.pageSize,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [Modal, modalApi] = useVbenModal({
  onOpened: () => {},
  onConfirm: async () => {
    const selectedRows = gridApi.grid.getCheckboxRecords();
    const processedRows = selectedRows.map(({ id: _id, ...rest }) => rest);
    modalApi.setData(processedRows);
    await modalApi.close();
  },
});
</script>

<template>
  <Modal title="选择应收账款" class="w-[80vw]">
    <Grid />
  </Modal>
</template>

<style></style>
