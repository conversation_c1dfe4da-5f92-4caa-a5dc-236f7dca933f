<script setup lang="ts">
import type { AssetPoolInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { getAssetPoolInfoApi } from '#/api';
import AssetDetail from '#/views/asset-securities/asset-pool/components/basic-asset-detail.vue';

defineEmits(['register']);

// 详情页样式配置
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};

// 初始化数据
const init = async (data: AssetPoolInfo) => {
  // 获取详情数据，如果有ID则从接口获取，否则使用传入的数据
  const info = data.id ? await getAssetPoolInfoApi(data.id as number) : data;
  poolForm.value = { ...poolForm.value, ...info };
};

const poolForm = ref<AssetPoolInfo>({});
const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup title="ABS基础资产信息" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="关联融资项目">
          {{ poolForm.projectName }}
        </a-descriptions-item>

        <a-descriptions-item label="ABS基础资产编号">
          {{ poolForm.absAssetCode }}
        </a-descriptions-item>

        <a-descriptions-item label="保理合同编号">
          {{ poolForm.factoringContractCode }}
        </a-descriptions-item>

        <a-descriptions-item label="ABS基础资产名称">
          {{ poolForm.absAssetName }}
        </a-descriptions-item>

        <a-descriptions-item label="保理融资金额（元）">
          {{ poolForm.financingAmount }}
        </a-descriptions-item>

        <a-descriptions-item label="应收账款总金额（元）">
          {{ poolForm.receivableAmount }}
        </a-descriptions-item>

        <a-descriptions-item label="融资利率（%）">
          {{ poolForm.financingRate }}
        </a-descriptions-item>
      </a-descriptions>

      <AssetDetail :info="poolForm" />
    </div>
  </BasicPopup>
</template>

<style></style>
