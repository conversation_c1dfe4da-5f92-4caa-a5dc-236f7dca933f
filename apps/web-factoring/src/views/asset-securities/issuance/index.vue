<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { AssetIssueInfo } from '#/api';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Dropdown, Menu, MenuItem, message, Space, TypographyLink } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getAssetIssuePageListApi, issueUploadApi } from '#/api';

import AssetIssueDetail from './issuance-detail.vue';
import AssetIssueEdit from './issuance-edit.vue';

const dictStore = useDictStore();

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectName',
      label: 'ABS项目名称',
    },
    {
      component: 'Input',
      fieldName: 'originalInvestorName',
      label: '原始权益人',
    },
    {
      component: 'Input',
      fieldName: 'planManager',
      label: '计划管理人',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList('FCT_ABS_ISSUE_STATUS'),
      },
    },
  ],
  commonConfig: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'projectName', title: 'ABS项目名称', minWidth: 200 },
    { field: 'originalInvestorName', title: '原始权益人' },
    { field: 'planManager', title: '计划管理人' },
    {
      field: 'status',
      title: '发行状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_ABS_ISSUE_STATUS',
        },
      },
    },
    { field: 'issueDate', title: '发行日期' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 120,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getAssetIssuePageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const edit = (row: AssetIssueInfo) => {
  openFormPopup(true, row);
};
const editSuccess = () => {
  gridApi.reload();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: AssetIssueInfo) => {
  openDetailPopup(true, row);
};
const uploadInfo = (row: any, type: string) => {
  uploadForm.value.type = type;
  uploadForm.value.id = row.id;
  uploadForm.value.label = {
    notify: '上传信息批露材料',
  }[type];
  uploadForm.value.businessType = {
    notify: 'FCT_ABS_ISSUE_DISCLOSURE',
  }[type];
  modalApi.open();
};
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    const api = {
      notify: issueUploadApi,
    }[uploadForm.value.type];
    await api(uploadForm.value);
    message.success($t('base.resSuccess'));
    await modalApi.close();
  },
  onClosed: () => {
    uploadForm.value = {
      businessType: '',
    };
  },
});
const uploadForm = ref({
  businessType: '',
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['0'].includes(row.status)" @click="edit(row)"> 发行 </a-typography-link>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
          <Dropdown v-if="['1'].includes(row.status)">
            <TypographyLink>
              <Space :size="0">
                {{ $t('base.more') }}
                <VbenIcon class="ml-1" icon="ant-design:down-outlined" />
              </Space>
            </TypographyLink>
            <template #overlay>
              <Menu>
                <MenuItem key="application" @click="uploadInfo(row, 'notify')"> 上传信息批露材料 </MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </a-space>
      </template>
    </Grid>
    <AssetIssueEdit @register="registerForm" @ok="editSuccess" />
    <AssetIssueDetail @register="registerDetail" />
    <Modal :title="uploadForm.label" class="w-[800px]">
      <BaseAttachmentList
        v-model="uploadForm.attachmentList"
        :business-id="uploadForm.id"
        :business-type="uploadForm.businessType"
        edit-mode
      />
    </Modal>
  </Page>
</template>

<style></style>
