import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface InitiationInfo {
  /**
   * 项目背景说明
   */
  backgroundDesc?: string;
  /**
   * 地市编码
   */
  cityCode?: string;
  /**
   * 地市
   */
  cityName?: string;
  /**
   * 关联企业列表
   */
  companyList?: ProjectCompanyBO[];
  /**
   * 授信额度（元）
   */
  creditAmount?: number;
  /**
   * 客户测算额度（元）
   */
  creditCalculateAmount?: number;
  /**
   * 增信措施说明
   */
  creditEnhancementDesc?: string;
  /**
   * 额度说明
   */
  creditLimitDesc?: string;
  /**
   * 最低用信期限（个月）
   */
  creditMinPeriod?: number;
  /**
   * 债权人基本情况说明
   */
  creditorInfoDesc?: string;
  /**
   * 其他情况说明
   */
  creditOtherDesc?: string;
  /**
   * 授信费率（%）
   */
  creditRate?: number;
  /**
   * 授信对象
   */
  creditRecipient?: string;
  /**
   * 授信对象基本情况
   */
  creditRecipientDesc?: string;
  /**
   * 单笔用信额度上限（元）
   */
  creditSingleMaxUsed?: number;
  /**
   * 授信期限（月）
   */
  creditTerm?: number;
  /**
   * 是否循环额度（授信方式）
   */
  creditType?: string;
  /**
   * 债务人基本情况说明
   */
  debtorInfoDesc?: string;
  /**
   * 区县编码
   */
  districtCode?: string;
  /**
   * 区县
   */
  districtName?: string;
  /**
   * 预估综合收益率（%）
   */
  estimateIncomeRate?: number;
  /**
   * 支持保理方向：1=正向保理，2=反向保理
   */
  factoringDirection?: string;
  /**
   * 保理类型：1=保理融资，2=再保理融资
   */
  factoringType?: string;
  /**
   * 保理融资金额（元）
   */
  financingAmount?: number;
  /**
   * 保理融资比例（%）
   */
  financingRatio?: number;
  /**
   * 资金用途说明
   */
  fundUsageDesc?: string;
  /**
   * 担保人基本情况说明
   */
  guarantorInfoDesc?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 是否脱核：0=否，1=是
   */
  isCoreDecoupled?: string;
  /**
   * 是否为省重点产业：0-否 1-是
   */
  isProvincialKeyIndustry?: number;
  /**
   * 是否审批
   */
  isReview?: number;
  /**
   * 是否支持实体经济：0-否 1-是
   */
  isSupportRealEconomy?: number;
  /**
   * 项目编号
   */
  projectCode?: string;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 项目类型
   */
  projectType?: string;
  /**
   * 应收账款描述
   */
  receivableDesc?: string;
  /**
   * 追索权要求：0=否，1=是
   */
  recourseRequired?: string;
  /**
   * 审批状态
   */
  reviewStatus?: string;
  /**
   * 风控措施描述
   */
  riskControlDesc?: string;
  /**
   * 服务费（元）
   */
  serviceAmount?: number;
  /**
   * 操作状态
   */
  status?: string;
  /**
   * 支持操作模式：1=明保理，2=暗保理
   */
  supportMode?: string;
  /**
   * 合作企业Code
   */
  targetCompanyCode?: number;
  /**
   * 合作企业名称
   */
  targetCompanyName?: string;
  /**
   * 投向行业
   */
  targetIndustry?: string;
  /**
   * 关联用户列表
   */
  userList?: ProjectUserBO[];
  [property: string]: any;
}
/**
 * ProjectCompanyBO，项目主体企业
 */
export interface ProjectCompanyBO {
  /**
   * 企业编码
   */
  companyCode?: string;
  /**
   * 企业名称
   */
  companyName?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 项目企业类型
   */
  projectCompanyType?: string;
  /**
   * 项目ID
   */
  projectId?: number;
  [property: string]: any;
}

/**
 * ProjectUserBO，项目管理人员
 */
export interface ProjectUserBO {
  /**
   * 主键
   */
  id?: number;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目人员类型
   */
  projectUserType?: string;
  /**
   * 用户ID
   */
  userId?: number;
  /**
   * 用户名称
   */
  userName?: string;
  [property: string]: any;
}

// 获取立项分页列表
export async function getInitiationPageListApi(params: PageListParams) {
  return requestClient.get<InitiationInfo[]>('/factoring/project/proposal/page', { params });
}

// 添加综合立项
export async function addComprehensiveInitiationApi(data: InitiationInfo) {
  return requestClient.post<InitiationInfo>('/factoring/project/proposal/comprehensive/add', data);
}

// 编辑综合立项
export async function editComprehensiveInitiationApi(data: InitiationInfo) {
  return requestClient.post<InitiationInfo>('/factoring/project/proposal/comprehensive/edit', data);
}

// 添加单一立项
export async function addSingleInitiationApi(data: InitiationInfo) {
  return requestClient.post<InitiationInfo>('/factoring/project/proposal/single/add', data);
}

// 编辑单一立项
export async function editSingleInitiationApi(data: InitiationInfo) {
  return requestClient.post<InitiationInfo>('/factoring/project/proposal/single/edit', data);
}
// 获取立项详情
export async function getInitiationInfoApi(id: number) {
  return requestClient.get<InitiationInfo>(`/factoring/project/proposal/detail/${id}`);
}

// 删除立项
export async function delInitiationApi(id: number) {
  return requestClient.post(`/factoring/project/proposal/delete/${id}`);
}

// 获取公司列表
export async function getCompanyListApi() {
  return requestClient.get('/base/company/list');
}

// // 获取项目列表
// export async function getProjectListApi(params: PageListParams) {
//   return requestClient.get('/factoring/project/proposal/list', { params });
// }

// 获取人员列表
export async function getUserListApi(params: PageListParams) {
  return requestClient.get('/upms/user/list', { params });
}

// 额度测算
export async function getProjectLimitApi(params: PageListParams) {
  return requestClient.get('/factoring/project/limit', { params });
}

// 根据企业code查询测算额度
export async function getCompanyLimitApi(id: number) {
  return requestClient.get<InitiationInfo>(`/factoring/company/limit/getCompanyLimit/${id}`);
}
