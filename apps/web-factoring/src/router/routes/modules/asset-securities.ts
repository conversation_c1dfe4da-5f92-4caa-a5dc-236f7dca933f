import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: '',
      title: $t('page.asset-securities.title'),
    },
    name: 'AssetSecurities',
    path: '/asset-securities',
    children: [
      {
        name: 'AssetSecuritiesProject',
        path: '/asset-securities/project',
        component: () => import('#/views/asset-securities/project/index.vue'),
        meta: {
          icon: '',
          title: $t('page.asset-securities.project'),
        },
      },
      {
        name: 'AssetSecuritiesBasicAsset',
        path: '/asset-securities/basic-asset',
        component: () => import('#/views/asset-securities/basic-asset/index.vue'),
        meta: {
          icon: '',
          title: $t('page.asset-securities.basicAsset'),
        },
      },
      {
        name: 'AssetSecuritiesAssetPool',
        path: '/asset-securities/asset-pool',
        component: () => import('#/views/asset-securities/asset-pool/index.vue'),
        meta: {
          icon: '',
          title: $t('page.asset-securities.assetPool'),
        },
      },
      {
        name: 'AssetSecuritiesIssuance',
        path: '/asset-securities/issuance',
        component: () => import('#/views/asset-securities/issuance/index.vue'),
        meta: {
          icon: '',
          title: $t('page.asset-securities.issuance'),
        },
      },
    ],
  },
];

export default routes;
