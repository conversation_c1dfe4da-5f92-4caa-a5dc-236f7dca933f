<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { VbenIcon } from '@vben-core/shadcn-ui';

import {
  type ProjectBaseInfo,
  type MeetingInfo,
  projectReviewPageApi,
  projectReviewCancelApi,
  projectReviewUploadSummaryApi,
  getCompanyApi,
  getUserListApi,
} from '#/api';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { Button, message, Modal, Space, Spin, TypographyLink } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import Edit from '../components/edit.vue';
import Detail from '../components/detail.vue';
import { BaseAttachmentList } from '#/adapter/base-ui';

const { getDictList } = useDictStore();

const sortKey = ref<string>('create_time');
const dataLoaded = ref(false); // 添加加载状态
const usersOptions = ref([]);
const companyOptions = ref([]);
// 获取用户列表
const getUserList = async () => {
  const res = await getUserListApi();
  Object.assign(usersOptions.value, res);
};
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};
// 同时执行两个异步请求
const loadData = async () => {
  try {
    await Promise.all([getCompanyList(), getUserList()]);
  } finally {
    dataLoaded.value = true;
  }
};

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '项目编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Select',
      fieldName: 'executorCompanyName',
      label: '贸易执行企业',
      componentProps: {
        options: companyOptions.value,
        fieldNames: { label: 'companyName', value: 'companyCode' },
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'businessStructure',
      label: '业务结构',
      componentProps: {
        options: getDictList('BUS_STRUCTURE'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'projectModel',
      label: '项目模式',
      componentProps: {
        options: getDictList('PROJECT_MODE'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'projectReviewStatus',
      label: '业务状态',
      componentProps: {
        options: getDictList('PROJECT_STATUS'),
        allowClear: true,
      },
    },
    // {
    //   component: 'Select',
    //   fieldName: 'approvalStatus',
    //   label: '审批状态',
    //   componentProps: {
    //     options: getDictList('REVIEW_STATUS'),
    //     allowClear: true,
    //   },
    // },
    {
      component: 'Select',
      fieldName: 'purchaseMode',
      label: '采购模式',
      componentProps: {
        options: getDictList('PURCHASE_MODE'),
        // mode: 'multiple',
        allowClear: true,
      },
    },
  ],
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'reviewNodeId', title: '项目评审编号' },
    { field: 'projectCode', title: '项目编号' },
    { field: 'projectName', title: '项目名称' },
    { field: 'businessStructure', title: '业务结构', formatter: ['formatStatus', 'BUS_STRUCTURE'] },
    { field: 'projectModel', title: '项目模式', formatter: ['formatStatus', 'PROJECT_MODE'] },
    { field: 'executorCompanyName', title: '贸易执行企业' },
    {
      field: 'purchaseMode',
      title: '采购模式',
      formatter: ({ cellValue }) => {
        if (!cellValue) return '';

        // 返回字符串，按逗号分割
        if (typeof cellValue === 'string') {
          const values = cellValue.split(',').filter((item) => item.trim() !== '');
          const dictList = getDictList('PURCHASE_MODE');

          return values
            .map((value) => {
              const dictItem = dictList.find((item) => item.value === value);
              return dictItem ? dictItem.label : value;
            })
            .join(', ');
        }
        return cellValue;
      },
    },
    {
      field: 'projectReviewStatus',
      title: '业务状态',
      formatter: ['formatStatus', 'PROJECT_STATUS'],
    },
    // {
    //   field: 'approvalStatus',
    //   title: '审批状态',
    //   formatter: ['formatStatus', 'REVIEW_STATUS'],
    // },
    {
      field: 'businessManager',
      title: '业务负责人',
      formatter: (managers) => {
        if (!managers.cellValue || !Array.isArray(managers.cellValue) || managers.cellValue.length === 0) {
          return '';
        }
        return managers.cellValue
          .map((manager) => manager.userName)
          .filter((name) => name)
          .join(', ');
      },
    },
    { field: 'planStartDate', title: '预计开始日期', formatter: 'formatDate' },
    // { field: 'createTime', title: '创建时间' },
    // { field: 'createBy', title: '创建人' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 200,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const processedFormValues = { ...formValues };

        // 如果 purchaseMode 是数组，则转换为逗号分隔的字符串
        // if (Array.isArray(processedFormValues.purchaseMode)) {
        //   processedFormValues.purchaseMode = processedFormValues.purchaseMode.join(',');
        // }

        return await projectReviewPageApi({
          current: page.currentPage,
          size: page.pageSize,
          descs: sortKey.value,
          ...processedFormValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [registerEditForm, { openPopup: openEditPopup }] = usePopup();
const [registerDetailForm, { openPopup: openDetailPopup }] = usePopup();

const meetingForm = reactive<MeetingInfo>({
  reviewNodeId: undefined,
  attachmentList: [],
});

const companySelectorModal = reactive({
  visible: false,
  selectRow: undefined,
});

const handleCompanySelectOk = async () => {
  if (companySelectorModal.selectRow.reviewNodeId) {
    meetingForm.reviewNodeId = companySelectorModal.selectRow.reviewNodeId;
    await projectReviewUploadSummaryApi(meetingForm);
    companySelectorModal.visible = false;
    await gridApi.formApi.submitForm();
  }
};

const handleCompanySelectCancel = () => {
  companySelectorModal.visible = false;
};

const projectType = 'review';
const edit = (row: ProjectBaseInfo) => {
  const editRow = { ...row, projectType: projectType };
  openEditPopup(true, editRow);
};
const detail = (row: ProjectBaseInfo) => {
  const detailRow = { ...row, projectType: projectType };
  openDetailPopup(true, detailRow);
};
const meeting = (row: ProjectBaseInfo) => {
  companySelectorModal.visible = true;
  companySelectorModal.selectRow = row;
};
const editSuccess = () => {
  gridApi.formApi.submitForm();
};
const detailSuccess = () => {
  gridApi.formApi.submitForm();
};

// const del = async (row: ProjectBaseInfo) => {
//   AntdModal.confirm({
//     title: $t('base.confirmDelTitle'),
//     content: $t('base.confirmDelContent'),
//     async onOk() {
//       try {
//         await projectProposalDeleteApi(row.id);
//         message.success($t('base.resSuccess'));
//         await gridApi.formApi.submitForm();
//       } catch (error: any) {
//         message.error('删除失败: ' + error.message);
//       }
//     },
//   });
// };

const cancel = async (row: ProjectBaseInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmCancelTitle'),
    content: $t('base.confirmCancelContent'),
    async onOk() {
      try {
        await projectReviewCancelApi(row.id);
        message.success($t('base.resSuccess'));
        await gridApi.formApi.submitForm();
      } catch (error: any) {
        message.error('作废失败: ' + error.message);
      }
    },
  });
};

onMounted(() => {
  loadData();
});
</script>

<template>
  <Page auto-content-height>
    <Grid v-if="dataLoaded">
      <template #action="{ row }">
        <Space>
          <TypographyLink v-if="row.projectReviewStatus === 'SUBMIT'" @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="cancel(row)"> 作废</TypographyLink>
          <TypographyLink @click="meeting(row)"> 会议纪要</TypographyLink>
        </Space>
      </template>
    </Grid>
    <div v-else class="flex h-64 items-center justify-center">
      <Spin size="large" />
    </div>
    <Edit @register="registerEditForm" @ok="editSuccess" />
    <Detail @register="registerDetailForm" @ok="detailSuccess" />

    <Modal
      v-model:open="companySelectorModal.visible"
      @ok="handleCompanySelectOk"
      @cancel="handleCompanySelectCancel"
      width="800px"
    >
      <BaseAttachmentList
        v-model="meetingForm.attachmentList"
        :business-id="meetingForm.reviewNodeId"
        business-type="SCM_PROJECT_REVIEW_RECORD"
        edit-mode
      >
        <template #header>
          <BasicCaption content="上传会议纪要" />
        </template>
      </BaseAttachmentList>
    </Modal>
  </Page>
</template>

<style></style>
