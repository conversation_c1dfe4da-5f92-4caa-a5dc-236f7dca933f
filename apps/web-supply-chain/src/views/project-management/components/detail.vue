<script setup lang="ts">
import { computed, watch, ref, reactive } from 'vue';

import {
  type ProjectBaseInfo,
  type ProjectPartners,
  projectReviewDetailApi,
  projectBranchDetailApi,
  projectGeneralDetailApi,
  projectProposalDetailApi,
  projectManageDetailApi,
  getCompanyApi,
  getUserListApi,
  getUserInfoByIdsApi,
  getUserListByKeywordApi,
  getUserTreeListApi,
} from '#/api';

import type { GridApi, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { BasicPopup, usePopupInner, FeUserSelect } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
// import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep, formatDate } from '@vben/utils';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BaseRegionPicker, BaseFileList, BaseFilePickList, BaseAttachmentList } from '#/adapter/base-ui';
import { message, Textarea, Descriptions, DescriptionsItem } from 'ant-design-vue';

const emit = defineEmits(['register', 'ok']);
const { getDictList } = useDictStore();

// 根据接口定义初始化产品信息
const defaultForm = {
  id: undefined,
  createTime: undefined,
  createBy: undefined,
  updateTime: undefined,
  updateBy: undefined,
  version: undefined,
  projectCode: new Date().getTime().toString(),
  projectName: undefined,
  executorCompanyCode: undefined,
  executorCompanyName: '江西财投集团有限责任公司',
  businessStructure: undefined,
  projectModel: undefined,
  purchaseMode: [],
  isGoodsControlMode: undefined,
  paymentTermDays: undefined,
  planStartDate: undefined,
  creditDueDate: undefined,
  expectedProjectScale: undefined,
  serviceFeeRate: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  detailAddress: undefined,
  remarks: undefined,
  isDeposit: undefined,
  mortgageInfoDesc: undefined,
  pledgeInfoDesc: undefined,
  mortgageInfoAttachment: undefined,
  pledgeInfoAttachment: undefined,
  guaranteeInfoDesc: undefined,
  riskControlDesc: undefined,
  creditEnhancementDesc: undefined,
  status: undefined,
  approvalStatus: undefined,
  paymentMethod: [],
  collectionMethod: [],
  settlementMethod: undefined,
  isKeyIndustry: undefined,
  isRealEnterprise: undefined,
  businessManagerId: [],
  operationManagerId: [],
  financeManagerId: [],
  riskManagerId: [],
  guaranteeCompanyCode: undefined,
  guaranteeCompanyName: undefined,
  creditType: undefined,
  projectReviewId: undefined,
  partyBranchId: undefined,
  generalManagerId: undefined,
  reviewNodeId: undefined,
  businessManager: [],
  operationManager: [],
  financeManager: [],
  riskManager: [],
  attachmentList: [],
  projectPartners: [
    {
      id: undefined,
      version: undefined,
      projectId: undefined,
      partnerType: undefined,
      companyCode: undefined,
      companyName: undefined,
      subLimitAmount: undefined,
      occupyLimit: undefined,
      creditType: undefined,
      expiryDate: undefined,
    },
  ],
};

let detailForm = reactive<Partial<ProjectBaseInfo>>(cloneDeep(defaultForm));
const pageLoading = ref(false);

const companyOptions = ref([]);
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};

// 处理逗号分隔字符串的函数
const processCommaSeparatedField = (fieldValue: any): string | undefined => {
  if (Array.isArray(fieldValue)) {
    return fieldValue.join(',');
  } else if (typeof fieldValue === 'string') {
    return fieldValue;
  }
  return undefined;
};

// 解析逗号分隔字符串为数组的函数
const parseCommaSeparatedField = (fieldValue: any): any[] => {
  if (typeof fieldValue === 'string' && fieldValue) {
    return fieldValue.split(',').filter((item) => item !== '');
  } else if (Array.isArray(fieldValue)) {
    return fieldValue;
  }
  return [];
};

const formRef = ref();
const currentProjectType = ref('');

const init = async (data: ProjectBaseInfo) => {
  pageLoading.value = true;
  await getCompanyList();
  if (data.id) {
    currentProjectType.value = data.projectType;
    console.log('当前:', currentProjectType.value);
    const apiMap = {
      review: projectReviewDetailApi,
      branch: projectBranchDetailApi,
      general: projectGeneralDetailApi,
      initiation: projectProposalDetailApi,
      info: projectManageDetailApi,
    };

    const api = apiMap[data.projectType as string] || projectReviewDetailApi;

    try {
      const nodeBasedTypes: (string | undefined)[] = ['review', 'branch', 'general'];
      const useReviewNodeId = nodeBasedTypes.includes(data.projectType);
      const queryId = useReviewNodeId && data.reviewNodeId ? data.reviewNodeId : data.id;

      const res: Partial<ProjectBaseInfo> = await api(queryId);

      Object.assign(detailForm, res);

      detailForm.purchaseMode = parseCommaSeparatedField(detailForm.purchaseMode);
      detailForm.paymentMethod = parseCommaSeparatedField(detailForm.paymentMethod);
      detailForm.collectionMethod = parseCommaSeparatedField(detailForm.collectionMethod);

      // 强制校验并转换projectPartners字段
      if (!Array.isArray(res.projectPartners) || res.projectPartners === null) {
        detailForm.projectPartners = [];
      } else {
        // 创建全新数组实例确保响应性
        detailForm.projectPartners = [...res.projectPartners];
      }

      // 强制刷新表格
      if (gridApiSupplier?.grid) {
        await gridApiSupplier.grid.reloadData(detailForm.projectPartners.filter((item) => item.partnerType === '1'));
      }
      if (gridApiPurchaser?.grid) {
        await gridApiPurchaser.grid.reloadData(detailForm.projectPartners.filter((item) => item.partnerType === '2'));
      }
      if (gridApiCredit?.grid) {
        await gridApiCredit.grid.reloadData(detailForm.projectPartners.filter((item) => item.partnerType === '3'));
      }
    } catch (error) {
      console.error('获取项目详情失败:', error);
      message.error('获取项目详情失败');
    }
  }
  pageLoading.value = false;
};

const titleMap = {
  review: '项目评审详情',
  branch: '支委会申请详情',
  general: '总经办申请详情',
  info: '项目信息详情',
  initiation: '项目立项详情',
} as const;

const title = computed(() => {
  return titleMap[currentProjectType.value as keyof typeof titleMap] || '';
});

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const close = () => {
  Object.assign(detailForm, cloneDeep(defaultForm));
};
const labelCol = { style: { width: '150px' } };

const accountList = ref<ProjectPartners[]>([]);

// 新增初始化方法
const setAccountData = (data: ProjectPartners[]) => {
  accountList.value = data;
  // 更新所有相关表格
  if (gridApiSupplier.grid) {
    gridApiSupplier.grid.reloadData(accountList.value);
  }
  if (gridApiPurchaser.grid) {
    gridApiPurchaser.grid.reloadData(accountList.value);
  }
  if (gridApiCredit.grid) {
    gridApiCredit.grid.reloadData(accountList.value);
  }
};

const gridOptions: VxeTableGridOptions = {
  showOverflow: true,
  keepSource: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const gridOptionsSupplier: VxeTableGridOptions = {
  ...gridOptions,
  columns: [
    {
      field: 'companyName',
      title: '企业名称',
      slots: { edit: 'edit_company_name' },
      minWidth: '160px',
    },
    {
      field: 'companyCode',
      title: '统一社会信用代码',
      slots: { edit: 'edit_company_code' },
      minWidth: '160px',
    },
  ],
  data: [],
};

const gridOptionsPurchaser: VxeTableGridOptions = {
  ...gridOptions,
  columns: [
    {
      field: 'companyName',
      title: '企业名称',
      slots: { edit: 'edit_company_name' },
      minWidth: '160px',
    },
    {
      field: 'companyCode',
      title: '统一社会信用代码',
      slots: { edit: 'edit_company_code' },
      minWidth: '160px',
    },
  ],
  data: [],
};

const gridOptionsCredit: VxeTableGridOptions = {
  ...gridOptions,
  columns: [
    {
      field: 'companyName',
      title: '企业名称',
      slots: { edit: 'edit_company_name' },
      minWidth: '160px',
    },
    {
      field: 'companyCode',
      title: '统一社会信用代码',
      slots: { edit: 'edit_company_code' },
      minWidth: '160px',
    },
    {
      field: 'subLimitAmount',
      title: '企业额度上限（元）',
      editRender: {},
      slots: { edit: 'edit_sub_limit_amount' },
      minWidth: '160px',
    },
    {
      field: 'occupyLimit',
      title: '是否占用企业总额度',
      editRender: {},
      slots: { edit: 'edit_occupy_limit' },
      formatter: ['formatStatus', 'baseBooleanType'],
      minWidth: '160px',
    },
    {
      field: 'expiryDate',
      title: '额度到期日',
      editRender: {},
      slots: { edit: 'edit_expiry_date' },
      minWidth: '160px',
    },
    {
      field: 'creditType',
      title: '授信类型',
      editRender: {},
      slots: { edit: 'edit_credit_type' },
      formatter: ['formatStatus', 'CREDIT_TYPE'],
      minWidth: '160px',
    },
  ],
  data: [],
};

const [GridSupplier, gridApiSupplier] = useVbenVxeGrid({
  gridOptions: gridOptionsSupplier,
  tableTitle: '上游企业',
});
const [GridPurchaser, gridApiPurchaser] = useVbenVxeGrid({
  gridOptions: gridOptionsPurchaser,
  tableTitle: '下游企业',
});
const [GridCredit, gridApiCredit] = useVbenVxeGrid({
  gridOptions: gridOptionsCredit,
  tableTitle: '终端企业',
});

const filteredProjectModeOptions = ref<any[]>([]);

// 处理多名负责人名称显示
const getManagerNames = (managers: any[] | undefined) => {
  if (!managers || !Array.isArray(managers) || managers.length === 0) {
    return '';
  }
  return managers
    .map((manager) => manager.userName)
    .filter((name) => name)
    .join(', ');
};

// 处理企业code转换为名称显示
const getCompanyLabel = (companyList: any[], code: string | undefined) => {
  if (!code) return '';
  const item = companyList.find((item) => item.companyCode === code);
  return item ? item.companyName : code;
};

// 处理单选值显示的函数
const getDictLabel = (dictList: any[], value: string | number) => {
  console.log(dictList, value);
  if (value === undefined || value === null) return '';

  const item = dictList.find((dict) => {
    // 使用严格相等前先转换为相同类型
    return String(dict.value) === String(value);
  });
  return item ? item.label : String(value);
};

// 处理多选值显示的函数（支持数组和逗号分隔字符串）
const getMultiDictLabels = (options: any[], values: any[] | string) => {
  // 处理空值情况
  if (!values || values.length === 0) return '--';

  let valueArray: string[] = [];

  // 如果是字符串，按逗号分割
  if (typeof values === 'string') {
    valueArray = values.split(',').filter((item) => item.trim() !== '');
  }
  // 如果是数组，直接使用
  else if (Array.isArray(values)) {
    valueArray = values.map(String); // 确保都是字符串
  }

  // 如果处理后仍然为空
  if (valueArray.length === 0) return '--';

  return valueArray
    .map((value) => {
      const option = options.find((opt) => String(opt.value) === value.trim());
      return option ? option.label : value;
    })
    .join(', ');
};

// 只监听真正需要的依赖
watch(
  [() => detailForm.id, () => detailForm.projectModel, () => detailForm.businessStructure],
  ([id, projectModel, businessStructure]) => {
    const options = getDictList('PROJECT_MODE');

    // 编辑模式且已有项目模式值时，返回所有选项
    if (id && projectModel) {
      filteredProjectModeOptions.value = options;
      return;
    }

    // 根据业务结构过滤选项
    filteredProjectModeOptions.value = options.map((option) => {
      if (businessStructure === 'PURCHASE') {
        if (option.value === 'BUILDING' || option.value === 'WAREHOUSE_SUPPLY') {
          return { ...option, disabled: true };
        }
      }
      return { ...option, disabled: false };
    });
  },
  { immediate: true }, // 立即执行一次
);

// 暴露方法给父组件
defineExpose({
  getAccountData() {
    // 合并所有表格的数据
    let allData: any[] = [];

    if (gridApiSupplier.grid) {
      const { tableData } = gridApiSupplier.grid.getTableData();
      allData = [...allData, ...tableData];
    }

    if (gridApiPurchaser.grid) {
      const { tableData } = gridApiPurchaser.grid.getTableData();
      allData = [...allData, ...tableData];
    }

    if (gridApiCredit.grid) {
      const { tableData } = gridApiCredit.grid.getTableData();
      allData = [...allData, ...tableData];
    }

    return allData;
  },
  setAccountData,
});
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" :loading="pageLoading" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <Descriptions v-bind="DESCRIPTIONS_PROP">
        <!-- 基本信息 -->
        <DescriptionsItem label="项目编号">
          <span v-if="detailForm.projectCode">
            {{ detailForm.projectCode }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="项目名称">
          <span v-if="detailForm.projectName">
            {{ detailForm.projectName }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="业务结构">
          <span v-if="detailForm.businessStructure">
            {{ getDictLabel(getDictList('BUS_STRUCTURE'), detailForm.businessStructure) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="项目模式">
          <span v-if="detailForm.projectModel">
            {{ getDictLabel(getDictList('PROJECT_MODE'), detailForm.projectModel) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="贸易执行企业">
          <span v-if="detailForm.executorCompanyName">
            {{ detailForm.executorCompanyName }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="业务负责人">
          <span v-if="detailForm.businessManagerId && detailForm.businessManagerId.length > 0">
            {{ getManagerNames(detailForm.businessManager) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="运营负责人">
          <span v-if="detailForm.operationManagerId && detailForm.operationManagerId.length > 0">
            {{ getManagerNames(detailForm.operationManager) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="财务负责人">
          <span v-if="detailForm.financeManagerId && detailForm.financeManagerId.length > 0">
            {{ getManagerNames(detailForm.financeManager) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="风控负责人">
          <span v-if="detailForm.riskManagerId && detailForm.riskManagerId.length > 0">
            {{ getManagerNames(detailForm.riskManager) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="是否有保证金">
          <span v-if="detailForm.isDeposit">
            {{ getDictLabel(getDictList('baseBooleanType'), detailForm.isDeposit) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="采购模式">
          <span v-if="detailForm.purchaseMode && detailForm.purchaseMode.length > 0">
            {{ getMultiDictLabels(getDictList('PURCHASE_MODE'), detailForm.purchaseMode) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="是否是控货模式">
          <span v-if="detailForm.isGoodsControlMode">
            {{ getDictLabel(getDictList('baseBooleanType'), detailForm.isGoodsControlMode) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="合作费率（年%）">
          <span v-if="detailForm.serviceFeeRate">
            {{ Number(detailForm.serviceFeeRate).toFixed(2) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="账期（天)">
          <span v-if="detailForm.paymentTermDays">
            {{ detailForm.paymentTermDays }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="项目总额度(元)">
          <span v-if="detailForm.expectedProjectScale">
            {{ Number(detailForm.expectedProjectScale).toFixed(2) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="预计开始日期">
          <span v-if="detailForm.planStartDate">
            {{ detailForm.planStartDate }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="授信到期日">
          <span v-if="detailForm.creditDueDate">
            {{ detailForm.creditDueDate }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="项目地点">
          <span v-if="detailForm.province"> {{ detailForm.province }} / </span>
          <span v-if="detailForm.city"> {{ detailForm.city }} / </span>
          <span v-if="detailForm.district">
            {{ detailForm.district }}
          </span>
          <span v-if="detailForm.detailAddress">
            {{ detailForm.detailAddress }}
          </span>
          <span v-if="!detailForm.province && !detailForm.city && !detailForm.district && !detailForm.detailAddress">
            -
          </span>
        </DescriptionsItem>
        <DescriptionsItem label="担保企业">
          <span v-if="detailForm.guaranteeCompanyCode">
            {{ getCompanyLabel(companyOptions, detailForm.guaranteeCompanyCode) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="付款方式">
          <span v-if="detailForm.paymentMethod">
            {{ getMultiDictLabels(getDictList('PAYMENT_WAY'), detailForm.paymentMethod) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="回款方式">
          <span v-if="detailForm.collectionMethod">
            {{ getMultiDictLabels(getDictList('PAYMENT_WAY'), detailForm.collectionMethod) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="结算方式">
          <span v-if="detailForm.settlementMethod">
            {{ getDictLabel(getDictList('SETTLEMENT_MODE'), detailForm.settlementMethod) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="授信类型">
          <span v-if="detailForm.creditType">
            {{ getDictLabel(getDictList('CREDIT_TYPE'), detailForm.creditType) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="是否支持重点产业链">
          <span v-if="detailForm.isKeyIndustry">
            {{ getDictLabel(getDictList('baseBooleanType'), detailForm.isKeyIndustry) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="是否支持实体企业">
          <span v-if="detailForm.isRealEnterprise">
            {{ getDictLabel(getDictList('baseBooleanType'), detailForm.isRealEnterprise) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="备注">
          <span v-if="detailForm.remarks">
            {{ detailForm.remarks }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
      </Descriptions>

      <!-- 关联企业及敞口信息 -->
      <BasicCaption content="关联企业及敞口信息" />
      <div>
        <GridSupplier>
          <template #edit_company_name="{ row }">
            {{ row.companyName }}
          </template>
          <template #edit_company_code="{ row }">
            {{ row.companyCode }}
          </template>
        </GridSupplier>
      </div>
      <div>
        <GridPurchaser>
          <template #edit_company_name="{ row }">
            {{ row.companyName }}
          </template>
          <template #edit_company_code="{ row }">
            {{ row.companyCode }}
          </template>
        </GridPurchaser>
      </div>
      <div>
        <GridCredit>
          <template #edit_company_name="{ row }">
            {{ row.companyName }}
          </template>
          <template #edit_company_code="{ row }">
            {{ row.companyCode }}
          </template>
          <template #edit_sub_limit_amount="{ row }">
            {{ row.subLimitAmount }}
          </template>
          <template #edit_occupy_limit="{ row }">
            <span v-if="row.occupyLimit">
              {{ getDictLabel(getDictList('baseBooleanType'), row.occupyLimit) }}
            </span>
          </template>
          <template #edit_expiry_date="{ row }">
            {{ row.expiryDate }}
          </template>
          <template #edit_credit_type="{ row }">
            <span v-if="row.creditType">
              {{ getDictLabel(getDictList('CREDIT_TYPE'), row.creditType) }}
            </span>
          </template>
        </GridCredit>
      </div>

      <!-- 增信措施 -->
      <BasicCaption content="增信措施" />
      <Descriptions v-bind="DESCRIPTIONS_PROP" class="mt-4">
        <DescriptionsItem label="抵押物附件信息" v-if="currentProjectType !== 'initiation'">
          <BaseFileList :model-value="detailForm.mortgageInfoAttachment" />
        </DescriptionsItem>
        <DescriptionsItem label="抵押物信息描述" v-if="currentProjectType !== 'initiation'">
          {{ detailForm.mortgageInfoDesc }}
        </DescriptionsItem>
        <DescriptionsItem label="质押物附件信息" v-if="currentProjectType !== 'initiation'">
          <BaseFileList :model-value="detailForm.pledgeInfoAttachment" />
        </DescriptionsItem>
        <DescriptionsItem label="质押物信息描述" v-if="currentProjectType !== 'initiation'">
          {{ detailForm.pledgeInfoDesc }}
        </DescriptionsItem>
        <DescriptionsItem label="风控措施描述" v-if="currentProjectType !== 'initiation'">
          {{ detailForm.riskControlDesc }}
        </DescriptionsItem>
        <DescriptionsItem label="增信措施描述">
          {{ detailForm.creditEnhancementDesc }}
        </DescriptionsItem>
      </Descriptions>

      <!-- 附件信息 -->
      <BaseAttachmentList v-model="detailForm.attachmentList" :business-id="detailForm.id" business-type="SCM_PROJECT">
        <template #header>
          <BasicCaption content="项目立项附件信息" />
        </template>
      </BaseAttachmentList>

      <!-- 项目评审上会资料 -->
      <BaseAttachmentList
        v-if="currentProjectType !== 'initiation'"
        v-model="detailForm.attachmentList"
        :business-id="detailForm.projectReviewId"
        business-type="SCM_PROJECT_REVIEW"
      >
        <template #header>
          <BasicCaption content="项目评审上会资料" />
        </template>
      </BaseAttachmentList>

      <!-- 项目评审会议纪要 -->
      <BaseAttachmentList
        v-if="currentProjectType !== 'initiation'"
        v-model="detailForm.attachmentList"
        :business-id="detailForm.projectReviewId"
        business-type="SCM_PROJECT_REVIEW_RECORD"
      >
        <template #header>
          <BasicCaption content="项目评审会议纪要" />
        </template>
      </BaseAttachmentList>

      <!-- 支委会上会资料 -->
      <BaseAttachmentList
        v-if="currentProjectType === 'branch' || currentProjectType === 'general' || currentProjectType === 'info'"
        v-model="detailForm.attachmentList"
        :business-id="detailForm.partyBranchId"
        business-type="SCM_PARTY_BRANCH"
      >
        <template #header>
          <BasicCaption content="支委会上会资料" />
        </template>
      </BaseAttachmentList>

      <!-- 支委会会议纪要 -->
      <BaseAttachmentList
        v-if="currentProjectType === 'branch' || currentProjectType === 'general' || currentProjectType === 'info'"
        v-model="detailForm.attachmentList"
        :business-id="detailForm.partyBranchId"
        business-type="SCM_PARTY_BRANCH_RECORD"
      >
        <template #header>
          <BasicCaption content="支委会会议纪要" />
        </template>
      </BaseAttachmentList>

      <!-- 总经办上会资料 -->
      <BaseAttachmentList
        v-if="currentProjectType === 'branch' || currentProjectType === 'general' || currentProjectType === 'info'"
        v-model="detailForm.attachmentList"
        :business-id="detailForm.generalManagerId"
        business-type="SCM_GENERAL_MANAGER"
      >
        <template #header>
          <BasicCaption content="总经办上会资料" />
        </template>
      </BaseAttachmentList>

      <!-- 总经办会议纪要 -->
      <BaseAttachmentList
        v-if="currentProjectType === 'branch' || currentProjectType === 'general' || currentProjectType === 'info'"
        v-model="detailForm.attachmentList"
        :business-id="detailForm.generalManagerId"
        business-type="SCM_GENERAL_MANAGER_RECORD"
      >
        <template #header>
          <BasicCaption content="总经办会议纪要" />
        </template>
      </BaseAttachmentList>
    </div>
  </BasicPopup>
</template>

<style scoped>
.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}

.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
