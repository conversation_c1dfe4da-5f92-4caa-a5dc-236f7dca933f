<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps, VxeGridPropTypes } from 'vxe-table';

import type { ContractInfo } from '#/api';

import { computed, ref, watch } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BasicCaption } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { Button, Input, Select, Space, TypographyLink } from 'ant-design-vue';
import { VxeGrid } from 'vxe-table';

import { BaseCloudDiskFilePicker } from '#/adapter/base-ui';
import {
  getBusinessContractListApi,
  getContractClassifyListApi,
  saveBatchTempContractSignApi,
  saveBusinessContractSignApi,
  saveTempContractSignApi,
} from '#/api';
import ContractDetailDialog from '#/components/contract/contract-detail-dialog.vue';
import ContractEditDialog from '#/components/contract/contract-edit-dialog.vue';

import CreateContractDialog from './create-contract-dialog.vue';

const props = defineProps({
  businessId: { type: Number, default: null },
  businessType: { type: String, required: true },
  editMode: { type: Boolean, default: false },
  tableClassName: { type: String, default: '' },
});
const dictStore = useDictStore();
const isTemp = computed(() => !props.businessId);
const contractIdList = defineModel({ type: Array });
const contractList = ref<ContractInfo[]>([]);
const baseColumns: VxeGridPropTypes.Columns = [
  { field: 'contractCode', title: '合同编号', editRender: {}, slots: { edit: 'contractCodeEdit' } },
  { field: 'contractName', title: '合同名称', editRender: {}, slots: { edit: 'contractNameEdit' } },
  // { field: 'status', title: '合同状态', formatter: ['formatStatus', 'CONTRACT_STATUS'] },
  {
    field: 'sealType',
    title: '用章类型',
    formatter: ['formatStatus', 'SEAL_TYPE'],
    editRender: {},
    slots: { edit: 'sealTypeEdit' },
  },
  { field: 'categoryName', title: '合同分类', editRender: {}, slots: { edit: 'categoryNameEdit' } },
  { field: 'action', title: '操作', slots: { default: 'action' } },
];
const editColumns: VxeGridPropTypes.Columns = [{ type: 'checkbox', width: '60px' }];
const columns = computed(() => {
  if (props.editMode) {
    return [...editColumns, ...baseColumns];
  }
  return [...baseColumns];
});
const gridOptions: VxeGridProps = {
  editConfig: {
    trigger: 'click',
    mode: 'row',
  },
  toolbarConfig: {
    refresh: false,
    custom: false,
    zoom: false,
    slots: {
      tools: 'toolbarTools',
    },
  },
};
const gridEvents: VxeGridListeners<ContractInfo> = {
  editClosed({ row }) {
    const saveApi = isTemp.value ? saveTempContractSignApi : saveBusinessContractSignApi;
    saveApi(row);
  },
};

const CreateContractDialogRef = ref();
const GridRef = ref();
const init = async () => {
  if (props.businessId && props.businessType) {
    const res = await getBusinessContractListApi({
      businessId: props.businessId,
      businessType: props.businessType,
    });
    res.forEach((o: ContractInfo) => {
      o.type = 1;
    });
    contractList.value = res;
    contractIdList.value = contractList.value.map((o: ContractInfo) => {
      return { id: o.id, type: o.type };
    });
  }
};
watch(
  () => props.businessId,
  () => {
    init();
  },
  { immediate: true },
);
watch(
  () => props.businessType,
  () => {
    init();
  },
);
init();
const BaseCloudDiskFilePickerRef = ref();
const addByFile = async () => {
  const { id } = await BaseCloudDiskFilePickerRef.value.pick({ multiple: true });
  const res = await saveBatchTempContractSignApi(id);
  res.forEach((o: ContractInfo) => {
    contractList.value.push({ ...o, type: 0 });
  });
  contractIdList.value = contractList.value.map((o: ContractInfo) => {
    return { id: o.id, type: o.type };
  });
};
const addByTemplate = async () => {
  CreateContractDialogRef.value.create();
};
const remove = () => {
  const records = GridRef.value.getCheckboxRecords();
  contractList.value = contractList.value.filter((o: ContractInfo) => !records.includes(o));
  contractIdList.value = contractList.value.map((o: ContractInfo) => {
    return { id: o.id, type: o.type };
  });
};

const ContractEditDialogRef = ref();
const ContractDetailDialogRef = ref();
const addSuccess = (data: ContractInfo) => {
  contractList.value.push({ ...data, type: isTemp.value ? 0 : 1 });
  contractIdList.value = contractList.value.map((o: ContractInfo) => {
    return { id: o.id, type: o.type };
  });
};
const view = (row: ContractInfo) => {
  ContractDetailDialogRef.value.init(row, isTemp);
};
const edit = (row: ContractInfo) => {
  ContractEditDialogRef.value.init(row, isTemp);
};
</script>

<template>
  <div>
    <slot name="header">
      <BasicCaption content="合同文件" class="mb-4" />
    </slot>
    <VxeGrid
      ref="GridRef"
      :columns="columns"
      :data="contractList"
      :class="tableClassName"
      v-bind="gridOptions"
      v-on="gridEvents"
    >
      <template #toolbarTools>
        <Space v-if="editMode">
          <Button type="primary" @click="addByFile">上传文档</Button>
          <Button type="primary" @click="addByTemplate">选择模板</Button>
          <Button type="primary" ghost danger @click="remove">删除</Button>
        </Space>
      </template>
      <template #contractCodeEdit="{ row }">
        <Input v-model:value="row.contractCode" />
      </template>
      <template #contractNameEdit="{ row }">
        <Input v-model:value="row.contractName" />
      </template>
      <template #sealTypeEdit="{ row }">
        <Select
          v-model:value="row.sealType"
          :options="dictStore.getDictList('SEAL_TYPE')"
          popup-class-name="vxe-table--ignore-clear"
          class="w-full"
        />
      </template>
      <template #categoryNameEdit="{ row }">
        <ApiComponent
          :component="Select"
          v-model="row.categoryId as unknown as string"
          :api="getContractClassifyListApi"
          label-field="categoryName"
          value-field="id"
          model-prop-name="value"
        />
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink v-if="editMode" @click="edit(row)">编辑</TypographyLink>
          <TypographyLink @click="view(row)">详情</TypographyLink>
        </Space>
      </template>
    </VxeGrid>
    <CreateContractDialog
      ref="CreateContractDialogRef"
      :business-id="businessId"
      :business-type="businessType"
      @success="addSuccess"
    />
    <ContractEditDialog ref="ContractEditDialogRef" @success="init" />
    <ContractDetailDialog ref="ContractDetailDialogRef" />
    <BaseCloudDiskFilePicker ref="BaseCloudDiskFilePickerRef" />
  </div>
</template>

<style></style>
